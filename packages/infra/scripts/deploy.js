#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');

const STAGE = process.env.STAGE || 'dev';
const REGION = process.env.AWS_REGION || 'us-east-1';

console.log(`🚀 Deploying YouTube Chapter Generator to ${STAGE} in ${REGION}...`);

try {
  // Check if serverless is installed
  try {
    execSync('npx serverless --version', { stdio: 'pipe' });
  } catch (error) {
    console.log('📦 Installing Serverless Framework...');
    execSync('npm install -g serverless', { stdio: 'inherit' });
  }

  // Change to infra directory
  const infraDir = path.join(__dirname, '..');
  process.chdir(infraDir);

  console.log('🏗️  Deploying infrastructure...');

  // Deploy using serverless-compose
  const deployCmd = `npx serverless deploy --stage ${STAGE} --region ${REGION}`;
  console.log(`Running: ${deployCmd}`);

  execSync(deployCmd, {
    stdio: 'inherit',
    env: {
      ...process.env,
      STAGE,
      AWS_REGION: REGION,
    }
  });

  console.log('✅ Infrastructure deployed successfully!');

  // Build and deploy the Next.js app
  console.log('🔨 Building Next.js application...');
  const webDir = path.join(__dirname, '../../apps/web');
  process.chdir(webDir);

  // Install dependencies if needed
  execSync('npm ci', { stdio: 'inherit' });

  // Build the app
  execSync('npm run build', { stdio: 'inherit' });

  console.log('✅ Application built successfully!');
  console.log(`
🎉 Deployment completed!

Next steps:
1. Set environment variables for production:
   - NEXT_PUBLIC_STORAGE=dynamodb
   - AWS_REGION=${REGION}
   - JOBS_TABLE=youtube-chapter-infra-${STAGE}-jobs
   - USERS_TABLE=youtube-chapter-infra-${STAGE}-users
   - JWT_SECRET=<your-secure-secret>
   - GROQ_API_KEY=<your-groq-key>
   - STRIPE_SECRET_KEY=<your-stripe-key>

2. Deploy to your hosting platform (Vercel, AWS, etc.)

3. Update your DNS and SSL certificates

Infrastructure deployed to stage: ${STAGE}
Region: ${REGION}
  `);

} catch (error) {
  console.error('❌ Deployment failed:', error.message);
  process.exit(1);
}
