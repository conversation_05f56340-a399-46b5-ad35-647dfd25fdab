service: youtube-chapter-infra

provider:
  name: aws
  region: us-east-1
  runtime: nodejs20.x
  environment:
    JOBS_TABLE: ${self:custom.resourcesPrefix}-jobs
    USERS_TABLE: ${self:custom.resourcesPrefix}-users
    VIDEOS_TABLE: ${self:custom.resourcesPrefix}-videos
    CHAPTERS_TABLE: ${self:custom.resourcesPrefix}-chapters
    TRANSCRIPTS_TABLE: ${self:custom.resourcesPrefix}-transcripts
    AUDIO_BUCKET: ${self:custom.resourcesPrefix}-audio
    GROQ_API_KEY: ${env:GROQ_API_KEY}
    STRIPE_SECRET_KEY: ${env:STRIPE_SECRET_KEY}
    WEBSOCKET_ENDPOINT: ${env:WEBSOCKET_ENDPOINT, ''}
    SES_REGION: ${env:SES_REGION, ''}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:BatchWriteItem
      Resource:
        - !GetAtt UsersTable.Arn
        - !GetAtt JobsTable.Arn
        - !GetAtt VideosTable.Arn
        - !GetAtt ChaptersTable.Arn
        - !GetAtt TranscriptsTable.Arn
        - !Sub "${JobsTable.Arn}/index/*"
        - !Sub "${UsersTable.Arn}/index/*"
    - Effect: Allow
      Action:
        - s3:GetObject
        - s3:PutObject
        - s3:DeleteObject
      Resource: !Sub "${AudioBucket}/*"
    - Effect: Allow
      Action:
        - states:StartExecution
      Resource: !Ref ChapterStateMachine

custom:
  stage: ${opt:stage, 'dev'}
  resourcesPrefix: ${self:service}-${self:custom.stage}

functions:
  transcribe:
    handler: functions/transcribe.handler
    timeout: 300
    memorySize: 1024

  segment:
    handler: functions/segment.handler
    timeout: 60
    memorySize: 512

  title:
    handler: functions/title.handler
    timeout: 120
    memorySize: 512

  persist:
    handler: functions/persist.handler
    timeout: 60
    memorySize: 512

  bill:
    handler: functions/bill.handler
    timeout: 60
    memorySize: 256

  notify:
    handler: functions/notify.handler
    timeout: 60
    memorySize: 256

resources:
  Resources:
    UsersTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.resourcesPrefix}-users
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: email
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: gsi_email
            KeySchema:
              - AttributeName: email
                KeyType: HASH
            Projection:
              ProjectionType: ALL

    JobsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.resourcesPrefix}-jobs
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: job_id
            AttributeType: S
          - AttributeName: customer_id
            AttributeType: S
          - AttributeName: created_at
            AttributeType: S
        KeySchema:
          - AttributeName: job_id
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: gsi_customer_created
            KeySchema:
              - AttributeName: customer_id
                KeyType: HASH
              - AttributeName: created_at
                KeyType: RANGE
            Projection:
              ProjectionType: ALL
    AudioBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:custom.resourcesPrefix}-audio
        LifecycleConfiguration:
          Rules:
            - Id: ExpireAfter24h
              Status: Enabled
              ExpirationInDays: 1

    VideosTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.resourcesPrefix}-videos
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: video_id
            AttributeType: S
          - AttributeName: v
            AttributeType: S
        KeySchema:
          - AttributeName: video_id
            KeyType: HASH
          - AttributeName: v
            KeyType: RANGE

    ChaptersTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.resourcesPrefix}-chapters
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: video_id
            AttributeType: S
          - AttributeName: c
            AttributeType: S
        KeySchema:
          - AttributeName: video_id
            KeyType: HASH
          - AttributeName: c
            KeyType: RANGE

    TranscriptsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.resourcesPrefix}-transcripts
        BillingMode: PAY_PER_REQUEST
        TimeToLiveSpecification:
          AttributeName: expires_at
          Enabled: true
        AttributeDefinitions:
          - AttributeName: video_id
            AttributeType: S
        KeySchema:
          - AttributeName: video_id
            KeyType: HASH

    JobBus:
      Type: AWS::Events::EventBus
      Properties:
        Name: ${self:custom.resourcesPrefix}-bus

    JobRule:
      Type: AWS::Events::Rule
      Properties:
        Name: ${self:custom.resourcesPrefix}-rule
        EventBusName: !Ref JobBus
        EventPattern:
          source:
            - youtube.chapter
          detail-type:
            - VideoSubmitted
        Targets:
          - Id: ChapterStateMachine
            Arn: !GetAtt ChapterStateMachine.Arn

    ChapterStateMachine:
      Type: AWS::StepFunctions::StateMachine
      Properties:
        StateMachineName: ${self:custom.resourcesPrefix}-sfn
        StateMachineType: EXPRESS
        RoleArn: !GetAtt StepFunctionsRole.Arn
        DefinitionString: !Sub |
          {
            "Comment": "Transcribe → Segment → Title → Persist → Bill → Notify",
            "StartAt": "Transcribe",
            "States": {
              "Transcribe": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {
                  "FunctionName": "${TranscribeLambdaFunction}",
                  "Payload.$": "$"
                },
                "ResultPath": "$.transcribeResult",
                "Next": "Segment",
                "Retry": [
                  {
                    "ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"],
                    "IntervalSeconds": 2,
                    "MaxAttempts": 3,
                    "BackoffRate": 2
                  }
                ]
              },
              "Segment": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {
                  "FunctionName": "${SegmentLambdaFunction}",
                  "Payload.$": "$.transcribeResult.Payload"
                },
                "ResultPath": "$.segmentResult",
                "Next": "Title",
                "Retry": [
                  {
                    "ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"],
                    "IntervalSeconds": 2,
                    "MaxAttempts": 3,
                    "BackoffRate": 2
                  }
                ]
              },
              "Title": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {
                  "FunctionName": "${TitleLambdaFunction}",
                  "Payload.$": "$.segmentResult.Payload"
                },
                "ResultPath": "$.titleResult",
                "Next": "Persist",
                "Retry": [
                  {
                    "ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"],
                    "IntervalSeconds": 2,
                    "MaxAttempts": 3,
                    "BackoffRate": 2
                  }
                ]
              },
              "Persist": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {
                  "FunctionName": "${PersistLambdaFunction}",
                  "Payload.$": "$.titleResult.Payload"
                },
                "ResultPath": "$.persistResult",
                "Next": "Bill",
                "Retry": [
                  {
                    "ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"],
                    "IntervalSeconds": 2,
                    "MaxAttempts": 3,
                    "BackoffRate": 2
                  }
                ]
              },
              "Bill": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {
                  "FunctionName": "${BillLambdaFunction}",
                  "Payload.$": "$.persistResult.Payload"
                },
                "ResultPath": "$.billResult",
                "Next": "Notify",
                "Retry": [
                  {
                    "ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"],
                    "IntervalSeconds": 2,
                    "MaxAttempts": 3,
                    "BackoffRate": 2
                  }
                ]
              },
              "Notify": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {
                  "FunctionName": "${NotifyLambdaFunction}",
                  "Payload.$": "$.billResult.Payload"
                },
                "End": true,
                "Retry": [
                  {
                    "ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"],
                    "IntervalSeconds": 2,
                    "MaxAttempts": 3,
                    "BackoffRate": 2
                  }
                ]
              }
            }
          }

    StepFunctionsRole:
      Type: AWS::IAM::Role
      Properties:
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: states.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: StepFunctionsExecutionPolicy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - lambda:InvokeFunction
                  Resource:
                    - !GetAtt TranscribeLambdaFunction.Arn
                    - !GetAtt SegmentLambdaFunction.Arn
                    - !GetAtt TitleLambdaFunction.Arn
                    - !GetAtt PersistLambdaFunction.Arn
                    - !GetAtt BillLambdaFunction.Arn
                    - !GetAtt NotifyLambdaFunction.Arn

  Outputs:
    JobsTableName:
      Value: ${self:custom.resourcesPrefix}-jobs
      Export:
        Name: ${self:custom.resourcesPrefix}-jobs-table
    AudioBucket:
      Value: !Ref AudioBucket
      Export:
        Name: ${self:custom.resourcesPrefix}-audio-bucket
