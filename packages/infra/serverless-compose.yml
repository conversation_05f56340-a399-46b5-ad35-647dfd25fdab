service: youtube-chapter-infra

provider:
  name: aws
  region: us-east-1
  runtime: nodejs20.x

custom:
  stage: ${opt:stage, 'dev'}
  resourcesPrefix: ${self:service}-${self:custom.stage}

resources:
  Resources:
    JobsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.resourcesPrefix}-jobs
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: job_id
            AttributeType: S
          - AttributeName: customer_id
            AttributeType: S
          - AttributeName: created_at
            AttributeType: S
        KeySchema:
          - AttributeName: job_id
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: gsi_customer_created
            KeySchema:
              - AttributeName: customer_id
                KeyType: HASH
              - AttributeName: created_at
                KeyType: RANGE
            Projection:
              ProjectionType: ALL
    AudioBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:custom.resourcesPrefix}-audio
        LifecycleConfiguration:
          Rules:
            - Id: ExpireAfter24h
              Status: Enabled
              ExpirationInDays: 1

    VideosTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.resourcesPrefix}-videos
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: video_id
            AttributeType: S
          - AttributeName: v
            AttributeType: S
        KeySchema:
          - AttributeName: video_id
            KeyType: HASH
          - AttributeName: v
            KeyType: RANGE

    ChaptersTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.resourcesPrefix}-chapters
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: video_id
            AttributeType: S
          - AttributeName: c
            AttributeType: S
        KeySchema:
          - AttributeName: video_id
            KeyType: HASH
          - AttributeName: c
            KeyType: RANGE

    TranscriptsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.resourcesPrefix}-transcripts
        BillingMode: PAY_PER_REQUEST
        TimeToLiveSpecification:
          AttributeName: expires_at
          Enabled: true
        AttributeDefinitions:
          - AttributeName: video_id
            AttributeType: S
        KeySchema:
          - AttributeName: video_id
            KeyType: HASH

    JobBus:
      Type: AWS::Events::EventBus
      Properties:
        Name: ${self:custom.resourcesPrefix}-bus

    JobRule:
      Type: AWS::Events::Rule
      Properties:
        Name: ${self:custom.resourcesPrefix}-rule
        EventBusName: !Ref JobBus
        EventPattern:
          source:
            - youtube.chapter
          detail-type:
            - VideoSubmitted
        Targets:
          - Id: ChapterStateMachine
            Arn: !GetAtt ChapterStateMachine.Arn

    ChapterStateMachine:
      Type: AWS::StepFunctions::StateMachine
      Properties:
        StateMachineName: ${self:custom.resourcesPrefix}-sfn
        StateMachineType: EXPRESS
        DefinitionString: |
          {
            "Comment": "Transcribe → Segment → Title → Persist → Bill → Notify",
            "StartAt": "Transcribe",
            "States": {
              "Transcribe": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {"FunctionName": "${self:custom.resourcesPrefix}-transcribe"},
                "Next": "Segment"
              },
              "Segment": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {"FunctionName": "${self:custom.resourcesPrefix}-segment"},
                "Next": "Title"
              },
              "Title": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {"FunctionName": "${self:custom.resourcesPrefix}-title"},
                "Next": "Persist"
              },
              "Persist": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {"FunctionName": "${self:custom.resourcesPrefix}-persist"},
                "Next": "Bill"
              },
              "Bill": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {"FunctionName": "${self:custom.resourcesPrefix}-bill"},
                "Next": "Notify"
              },
              "Notify": {
                "Type": "Task",
                "Resource": "arn:aws:states:::lambda:invoke",
                "Parameters": {"FunctionName": "${self:custom.resourcesPrefix}-notify"},
                "End": true
              }
            }
          }

  Outputs:
    JobsTableName:
      Value: ${self:custom.resourcesPrefix}-jobs
      Export:
        Name: ${self:custom.resourcesPrefix}-jobs-table
    AudioBucket:
      Value: !Ref AudioBucket
      Export:
        Name: ${self:custom.resourcesPrefix}-audio-bucket
