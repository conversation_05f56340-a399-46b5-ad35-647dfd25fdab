const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, UpdateCommand } = require('@aws-sdk/lib-dynamodb');

const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({ region: process.env.AWS_REGION }));

exports.handler = async (event) => {
  console.log('Segment function started:', JSON.stringify(event, null, 2));
  
  try {
    const { job_id, video_id, transcript } = event;
    
    if (!job_id || !video_id || !transcript) {
      throw new Error('Missing required parameters: job_id, video_id, transcript');
    }

    // Update job status to segmenting
    await dynamoClient.send(new UpdateCommand({
      TableName: process.env.JOBS_TABLE,
      Key: { job_id },
      UpdateExpression: 'SET #status = :status, updated_at = :timestamp',
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: {
        ':status': 'segmenting',
        ':timestamp': new Date().toISOString(),
      },
    }));

    const { segments, duration } = transcript;
    
    // Calculate target chapter count based on duration
    const durationMinutes = Math.ceil(duration / 60);
    const targetCount = Math.max(6, Math.min(25, Math.ceil(durationMinutes / 6)));
    
    // Generate chapters using transcript-based segmentation
    const chapters = segmentByTranscript(segments, targetCount);
    
    console.log(`Generated ${chapters.length} chapters for video ${video_id}`);

    return {
      job_id,
      video_id,
      chapters,
      duration_minutes: durationMinutes,
    };

  } catch (error) {
    console.error('Segmentation failed:', error);
    
    // Update job status to failed
    try {
      await dynamoClient.send(new UpdateCommand({
        TableName: process.env.JOBS_TABLE,
        Key: { job_id: event.job_id },
        UpdateExpression: 'SET #status = :status, error = :error, updated_at = :timestamp',
        ExpressionAttributeNames: { '#status': 'status' },
        ExpressionAttributeValues: {
          ':status': 'failed',
          ':error': error.message,
          ':timestamp': new Date().toISOString(),
        },
      }));
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }
    
    throw error;
  }
};

function segmentByTranscript(segments, targetCount) {
  if (!segments || segments.length === 0) {
    return segmentByLength(600, targetCount); // Fallback to 10-minute default
  }

  const totalDuration = segments[segments.length - 1]?.end || 600;
  const targetDuration = totalDuration / targetCount;
  
  const chapters = [];
  let currentStart = 0;
  let currentSegments = [];
  
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];
    currentSegments.push(segment);
    
    const currentDuration = segment.end - currentStart;
    const isLastSegment = i === segments.length - 1;
    const shouldBreak = currentDuration >= targetDuration * 0.8 || isLastSegment;
    
    if (shouldBreak && (chapters.length < targetCount - 1 || isLastSegment)) {
      const chapterText = currentSegments.map(s => s.text).join(' ');
      const title = generateChapterTitle(chapterText, chapters.length + 1);
      
      chapters.push({
        start: formatTimestamp(currentStart),
        end: formatTimestamp(segment.end),
        duration_sec: Math.round(segment.end - currentStart),
        title,
      });
      
      currentStart = segment.end;
      currentSegments = [];
    }
  }
  
  return chapters;
}

function segmentByLength(totalSeconds, targetCount) {
  const segmentDuration = totalSeconds / targetCount;
  const chapters = [];
  
  for (let i = 0; i < targetCount; i++) {
    const start = i * segmentDuration;
    const end = Math.min((i + 1) * segmentDuration, totalSeconds);
    
    chapters.push({
      start: formatTimestamp(start),
      end: formatTimestamp(end),
      duration_sec: Math.round(end - start),
      title: `Section ${i + 1}`,
    });
  }
  
  return chapters;
}

function generateChapterTitle(text, index) {
  if (!text || text.trim().length === 0) {
    return `Section ${index}`;
  }
  
  // Simple title generation - extract first meaningful phrase
  const words = text.trim().split(/\s+/).slice(0, 8);
  let title = words.join(' ');
  
  // Clean up title
  title = title.replace(/[^\w\s-]/g, '').trim();
  
  if (title.length === 0) {
    return `Section ${index}`;
  }
  
  // Capitalize first letter
  title = title.charAt(0).toUpperCase() + title.slice(1);
  
  return title.length > 50 ? title.substring(0, 47) + '...' : title;
}

function formatTimestamp(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}
