const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, UpdateCommand } = require('@aws-sdk/lib-dynamodb');

const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({ region: process.env.AWS_REGION }));

exports.handler = async (event) => {
  console.log('Title function started:', JSON.stringify(event, null, 2));
  
  try {
    const { job_id, video_id, chapters } = event;
    
    if (!job_id || !video_id || !chapters) {
      throw new Error('Missing required parameters: job_id, video_id, chapters');
    }

    // Update job status to titling
    await dynamoClient.send(new UpdateCommand({
      TableName: process.env.JOBS_TABLE,
      Key: { job_id },
      UpdateExpression: 'SET #status = :status, updated_at = :timestamp',
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: {
        ':status': 'titling',
        ':timestamp': new Date().toISOString(),
      },
    }));

    // Enhance chapter titles using LLM if available
    const enhancedChapters = await enhanceChapterTitles(chapters);
    
    console.log(`Enhanced titles for ${enhancedChapters.length} chapters`);

    return {
      job_id,
      video_id,
      chapters: enhancedChapters,
    };

  } catch (error) {
    console.error('Title enhancement failed:', error);
    
    // Update job status to failed
    try {
      await dynamoClient.send(new UpdateCommand({
        TableName: process.env.JOBS_TABLE,
        Key: { job_id: event.job_id },
        UpdateExpression: 'SET #status = :status, error = :error, updated_at = :timestamp',
        ExpressionAttributeNames: { '#status': 'status' },
        ExpressionAttributeValues: {
          ':status': 'failed',
          ':error': error.message,
          ':timestamp': new Date().toISOString(),
        },
      }));
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }
    
    throw error;
  }
};

async function enhanceChapterTitles(chapters) {
  const groqApiKey = process.env.GROQ_API_KEY;
  
  if (!groqApiKey) {
    console.log('No GROQ_API_KEY found, using basic titles');
    return chapters.map((chapter, index) => ({
      ...chapter,
      title: chapter.title || `Chapter ${index + 1}`,
    }));
  }

  try {
    // Prepare prompt for title enhancement
    const chaptersText = chapters.map((ch, i) => 
      `${i + 1}. ${ch.start} - ${ch.end}: "${ch.title}"`
    ).join('\n');

    const prompt = `Improve these YouTube chapter titles to be more descriptive and SEO-friendly. Keep them concise (under 50 characters) and engaging:

${chaptersText}

Return only the improved titles, one per line, in the same order:`;

    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${groqApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama3-8b-8192',
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 500,
        temperature: 0.3,
      }),
    });

    if (!response.ok) {
      console.warn(`Groq API error: ${response.status}, falling back to basic titles`);
      return chapters;
    }

    const result = await response.json();
    const enhancedTitles = result.choices[0]?.message?.content
      ?.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);

    if (enhancedTitles && enhancedTitles.length === chapters.length) {
      return chapters.map((chapter, index) => ({
        ...chapter,
        title: enhancedTitles[index] || chapter.title,
      }));
    } else {
      console.warn('LLM returned unexpected number of titles, using originals');
      return chapters;
    }

  } catch (error) {
    console.warn('Title enhancement failed, using basic titles:', error.message);
    return chapters;
  }
}
