const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, UpdateCommand, GetCommand } = require('@aws-sdk/lib-dynamodb');
const { ApiGatewayManagementApiClient, PostToConnectionCommand } = require('@aws-sdk/client-apigatewaymanagementapi');

const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({ region: process.env.AWS_REGION }));

exports.handler = async (event) => {
  console.log('Notify function started:', JSON.stringify(event, null, 2));
  
  try {
    const { job_id, video_id, chapters, minutes_billed } = event;
    
    if (!job_id || !video_id) {
      throw new Error('Missing required parameters: job_id, video_id');
    }

    // Update job status to notifying
    await dynamoClient.send(new UpdateCommand({
      TableName: process.env.JOBS_TABLE,
      Key: { job_id },
      UpdateExpression: 'SET #status = :status, updated_at = :timestamp',
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: {
        ':status': 'notifying',
        ':timestamp': new Date().toISOString(),
      },
    }));

    // Get job details to find customer
    const jobResult = await dynamoClient.send(new GetCommand({
      TableName: process.env.JOBS_TABLE,
      Key: { job_id },
    }));

    const job = jobResult.Item;
    if (!job) {
      throw new Error(`Job ${job_id} not found`);
    }

    // Send WebSocket notification if endpoint is configured
    const wsEndpoint = process.env.WEBSOCKET_ENDPOINT;
    if (wsEndpoint && job.customer_id) {
      await sendWebSocketNotification(wsEndpoint, job.customer_id, {
        type: 'job.completed',
        job_id,
        video_id,
        status: 'completed',
        chapters: chapters || [],
        minutes_billed: minutes_billed || 0,
      });
    }

    // Send email notification if configured
    const sesRegion = process.env.SES_REGION;
    if (sesRegion && job.customer_email) {
      await sendEmailNotification(job.customer_email, {
        job_id,
        video_id,
        chapters: chapters || [],
      });
    }

    // Final job status update
    await dynamoClient.send(new UpdateCommand({
      TableName: process.env.JOBS_TABLE,
      Key: { job_id },
      UpdateExpression: 'SET #status = :status, updated_at = :timestamp',
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: {
        ':status': 'completed',
        ':timestamp': new Date().toISOString(),
      },
    }));

    console.log(`Notification sent for job ${job_id}`);

    return {
      job_id,
      video_id,
      status: 'completed',
      notification_sent: true,
    };

  } catch (error) {
    console.error('Notification failed:', error);
    
    // Update job status to failed
    try {
      await dynamoClient.send(new UpdateCommand({
        TableName: process.env.JOBS_TABLE,
        Key: { job_id: event.job_id },
        UpdateExpression: 'SET #status = :status, error = :error, updated_at = :timestamp',
        ExpressionAttributeNames: { '#status': 'status' },
        ExpressionAttributeValues: {
          ':status': 'failed',
          ':error': error.message,
          ':timestamp': new Date().toISOString(),
        },
      }));
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }
    
    throw error;
  }
};

async function sendWebSocketNotification(endpoint, customerId, payload) {
  try {
    // In a real implementation, you'd query for active connections for this customer
    // For now, we'll just log the notification
    console.log(`WebSocket notification for customer ${customerId}:`, payload);
    
    // Example WebSocket implementation:
    // const apiGateway = new ApiGatewayManagementApiClient({ endpoint });
    // const connections = await getCustomerConnections(customerId);
    // 
    // for (const connectionId of connections) {
    //   try {
    //     await apiGateway.send(new PostToConnectionCommand({
    //       ConnectionId: connectionId,
    //       Data: JSON.stringify(payload),
    //     }));
    //   } catch (error) {
    //     if (error.statusCode === 410) {
    //       // Connection is stale, remove it
    //       await removeConnection(connectionId);
    //     }
    //   }
    // }
    
  } catch (error) {
    console.error('WebSocket notification failed:', error);
  }
}

async function sendEmailNotification(email, data) {
  try {
    // Email notification implementation would go here
    // Using SES or another email service
    console.log(`Email notification to ${email}:`, data);
    
    // Example SES implementation:
    // const ses = new SESClient({ region: process.env.SES_REGION });
    // await ses.send(new SendEmailCommand({
    //   Source: process.env.FROM_EMAIL,
    //   Destination: { ToAddresses: [email] },
    //   Message: {
    //     Subject: { Data: 'Your YouTube chapters are ready!' },
    //     Body: {
    //       Html: { Data: generateEmailHtml(data) },
    //       Text: { Data: generateEmailText(data) },
    //     },
    //   },
    // }));
    
  } catch (error) {
    console.error('Email notification failed:', error);
  }
}
