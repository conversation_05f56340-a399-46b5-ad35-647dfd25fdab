const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, UpdateCommand, PutCommand, BatchWriteCommand } = require('@aws-sdk/lib-dynamodb');

const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({ region: process.env.AWS_REGION }));

exports.handler = async (event) => {
  console.log('Persist function started:', JSON.stringify(event, null, 2));
  
  try {
    const { job_id, video_id, chapters, duration_minutes } = event;
    
    if (!job_id || !video_id || !chapters) {
      throw new Error('Missing required parameters: job_id, video_id, chapters');
    }

    // Update job status to persisting
    await dynamoClient.send(new UpdateCommand({
      TableName: process.env.JOBS_TABLE,
      Key: { job_id },
      UpdateExpression: 'SET #status = :status, updated_at = :timestamp',
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: {
        ':status': 'persisting',
        ':timestamp': new Date().toISOString(),
      },
    }));

    // Save video record
    await dynamoClient.send(new PutCommand({
      TableName: process.env.VIDEOS_TABLE,
      Item: {
        video_id,
        v: 'v1',
        duration_sec: duration_minutes * 60,
        created_at: new Date().toISOString(),
        minutes_billed: duration_minutes,
        status: 'completed',
      },
    }));

    // Save chapters in batches
    await saveChaptersBatch(video_id, chapters);

    // Update job with final results
    await dynamoClient.send(new UpdateCommand({
      TableName: process.env.JOBS_TABLE,
      Key: { job_id },
      UpdateExpression: 'SET #status = :status, chapters = :chapters, minutes_billed = :minutes, completed_at = :timestamp, updated_at = :timestamp',
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: {
        ':status': 'completed',
        ':chapters': chapters,
        ':minutes': duration_minutes,
        ':timestamp': new Date().toISOString(),
      },
    }));

    console.log(`Persisted ${chapters.length} chapters for video ${video_id}`);

    return {
      job_id,
      video_id,
      chapters,
      minutes_billed: duration_minutes,
      status: 'completed',
    };

  } catch (error) {
    console.error('Persistence failed:', error);
    
    // Update job status to failed
    try {
      await dynamoClient.send(new UpdateCommand({
        TableName: process.env.JOBS_TABLE,
        Key: { job_id: event.job_id },
        UpdateExpression: 'SET #status = :status, error = :error, updated_at = :timestamp',
        ExpressionAttributeNames: { '#status': 'status' },
        ExpressionAttributeValues: {
          ':status': 'failed',
          ':error': error.message,
          ':timestamp': new Date().toISOString(),
        },
      }));
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }
    
    throw error;
  }
};

async function saveChaptersBatch(videoId, chapters) {
  const batchSize = 25; // DynamoDB batch write limit
  
  for (let i = 0; i < chapters.length; i += batchSize) {
    const batch = chapters.slice(i, i + batchSize);
    
    const putRequests = batch.map((chapter, index) => ({
      PutRequest: {
        Item: {
          video_id: videoId,
          c: `c#${String(i + index).padStart(3, '0')}`,
          start: chapter.start,
          end: chapter.end,
          duration_sec: chapter.duration_sec,
          title: chapter.title,
          summary: chapter.summary || null,
          sequence: i + index,
        },
      },
    }));

    await dynamoClient.send(new BatchWriteCommand({
      RequestItems: {
        [process.env.CHAPTERS_TABLE]: putRequests,
      },
    }));
  }
}
