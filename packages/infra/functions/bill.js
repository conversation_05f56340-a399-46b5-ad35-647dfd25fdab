const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, UpdateCommand, GetCommand } = require('@aws-sdk/lib-dynamodb');

const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({ region: process.env.AWS_REGION }));

exports.handler = async (event) => {
  console.log('Bill function started:', JSON.stringify(event, null, 2));
  
  try {
    const { job_id, video_id, minutes_billed } = event;
    
    if (!job_id || !video_id || minutes_billed === undefined) {
      throw new Error('Missing required parameters: job_id, video_id, minutes_billed');
    }

    // Update job status to billing
    await dynamoClient.send(new UpdateCommand({
      TableName: process.env.JOBS_TABLE,
      Key: { job_id },
      UpdateExpression: 'SET #status = :status, updated_at = :timestamp',
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: {
        ':status': 'billing',
        ':timestamp': new Date().toISOString(),
      },
    }));

    // Get customer info from job
    const jobResult = await dynamoClient.send(new GetCommand({
      TableName: process.env.JOBS_TABLE,
      Key: { job_id },
    }));

    const job = jobResult.Item;
    if (!job) {
      throw new Error(`Job ${job_id} not found`);
    }

    const customerId = job.customer_id;
    if (!customerId) {
      console.warn('No customer_id found for job, skipping billing');
      return event;
    }

    // Post usage to Stripe if configured
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    if (stripeSecretKey) {
      await postStripeUsage(customerId, minutes_billed, job_id);
    } else {
      console.log('No Stripe key configured, skipping usage record');
    }

    console.log(`Billing completed for ${minutes_billed} minutes`);

    return {
      ...event,
      billing_completed: true,
    };

  } catch (error) {
    console.error('Billing failed:', error);
    
    // Update job status to failed
    try {
      await dynamoClient.send(new UpdateCommand({
        TableName: process.env.JOBS_TABLE,
        Key: { job_id: event.job_id },
        UpdateExpression: 'SET #status = :status, error = :error, updated_at = :timestamp',
        ExpressionAttributeNames: { '#status': 'status' },
        ExpressionAttributeValues: {
          ':status': 'failed',
          ':error': error.message,
          ':timestamp': new Date().toISOString(),
        },
      }));
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }
    
    throw error;
  }
};

async function postStripeUsage(customerId, minutes, jobId) {
  try {
    const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    
    // Find the customer's subscription
    const subscriptions = await stripe.subscriptions.list({
      customer: customerId,
      status: 'active',
      limit: 1,
    });

    if (subscriptions.data.length === 0) {
      console.warn(`No active subscription found for customer ${customerId}`);
      return;
    }

    const subscription = subscriptions.data[0];
    
    // Find the metered price item
    const meteredItem = subscription.items.data.find(item => 
      item.price.recurring?.usage_type === 'metered'
    );

    if (!meteredItem) {
      console.warn(`No metered price item found for customer ${customerId}`);
      return;
    }

    // Create usage record
    await stripe.subscriptionItems.createUsageRecord(meteredItem.id, {
      quantity: minutes,
      timestamp: Math.floor(Date.now() / 1000),
      action: 'increment',
    });

    console.log(`Posted ${minutes} minutes usage to Stripe for customer ${customerId}`);

  } catch (error) {
    console.error('Stripe usage posting failed:', error);
    // Don't throw - billing failure shouldn't fail the entire job
  }
}
