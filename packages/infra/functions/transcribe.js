const { S3Client, GetObjectCommand } = require('@aws-sdk/client-s3');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, UpdateCommand } = require('@aws-sdk/lib-dynamodb');

const s3Client = new S3Client({ region: process.env.AWS_REGION });
const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({ region: process.env.AWS_REGION }));

exports.handler = async (event) => {
  console.log('Transcribe function started:', JSON.stringify(event, null, 2));
  
  try {
    const { job_id, video_id, s3_key } = event;
    
    if (!job_id || !video_id || !s3_key) {
      throw new Error('Missing required parameters: job_id, video_id, s3_key');
    }

    // Update job status to transcribing
    await dynamoClient.send(new UpdateCommand({
      TableName: process.env.JOBS_TABLE,
      Key: { job_id },
      UpdateExpression: 'SET #status = :status, updated_at = :timestamp',
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: {
        ':status': 'transcribing',
        ':timestamp': new Date().toISOString(),
      },
    }));

    // Get audio file from S3
    const audioObject = await s3Client.send(new GetObjectCommand({
      Bucket: process.env.AUDIO_BUCKET,
      Key: s3_key,
    }));

    // Convert stream to buffer
    const audioBuffer = await streamToBuffer(audioObject.Body);

    // Call Groq Whisper API
    const groqApiKey = process.env.GROQ_API_KEY;
    if (!groqApiKey) {
      throw new Error('GROQ_API_KEY not configured');
    }

    const formData = new FormData();
    formData.append('file', new Blob([audioBuffer]), 'audio.mp3');
    formData.append('model', process.env.GROQ_WHISPER_MODEL || 'whisper-large-v3');
    formData.append('response_format', 'verbose_json');

    const response = await fetch('https://api.groq.com/openai/v1/audio/transcriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${groqApiKey}`,
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Groq API error: ${response.status} ${response.statusText}`);
    }

    const transcription = await response.json();
    
    // Store transcript in DynamoDB with TTL
    const expiresAt = Math.floor(Date.now() / 1000) + (24 * 60 * 60); // 24 hours
    
    await dynamoClient.send(new UpdateCommand({
      TableName: process.env.TRANSCRIPTS_TABLE,
      Key: { video_id },
      UpdateExpression: 'SET segments = :segments, language = :language, model_meta = :meta, expires_at = :expires',
      ExpressionAttributeValues: {
        ':segments': transcription.segments || [],
        ':language': transcription.language || 'en',
        ':meta': {
          model: process.env.GROQ_WHISPER_MODEL || 'whisper-large-v3',
          duration: transcription.duration,
        },
        ':expires': expiresAt,
      },
    }));

    console.log(`Transcription completed for video ${video_id}`);

    return {
      job_id,
      video_id,
      transcript: {
        segments: transcription.segments || [],
        language: transcription.language || 'en',
        duration: transcription.duration,
      },
    };

  } catch (error) {
    console.error('Transcription failed:', error);
    
    // Update job status to failed
    try {
      await dynamoClient.send(new UpdateCommand({
        TableName: process.env.JOBS_TABLE,
        Key: { job_id: event.job_id },
        UpdateExpression: 'SET #status = :status, error = :error, updated_at = :timestamp',
        ExpressionAttributeNames: { '#status': 'status' },
        ExpressionAttributeValues: {
          ':status': 'failed',
          ':error': error.message,
          ':timestamp': new Date().toISOString(),
        },
      }));
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }
    
    throw error;
  }
};

async function streamToBuffer(stream) {
  const chunks = [];
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks);
}
