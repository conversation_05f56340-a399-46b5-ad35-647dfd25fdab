Bulk YouTube Chapter Generator — Demo App (Edge API + UI)

## Getting Started

1) Run the development server:

```bash
npm run dev
```

2) Open http://localhost:3000

3) Submit a YouTube URL. The app queues a job, simulates orchestration, and returns demo chapters.

Notes:
- Plan selector sends `x-plan` header (`starter`/`creator`/`pro`) to simulate allowances (120/400/1600 min)
- Usage at `/api/usage` reflects in-memory minutes used
- <PERSON>uota exceeded returns 402 with a JSON error payload

Key routes:

- POST `/api/generate`: submit a URL; accepts optional `Idempotency-Key`, `Authorization`, and `x-plan`
- GET `/api/jobs/[jobId]`: fetch job status/results (no-store cache header)
- GET `/api/usage`: usage summary
- POST `/api/worker`: manually drain the in-memory event queue (auto-drained in dev)
- GET/DELETE `/api/dlq`: inspect/purge the in-memory DLQ

OpenAPI spec lives at `public/openapi.yaml`.
