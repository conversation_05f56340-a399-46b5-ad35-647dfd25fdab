# YouTube Chapter Generator

A production-ready SaaS application that automatically generates timestamped chapters for YouTube videos using AI transcription and intelligent segmentation.

## 🚀 Features

- **AI-Powered Transcription**: Uses Groq Whisper for fast, accurate transcription
- **Smart Chapter Generation**: Length-based and transcript-based segmentation
- **Enhanced Titles**: AI-generated descriptive chapter titles
- **User Authentication**: Secure JWT-based auth with signup/signin
- **Subscription Billing**: Stripe integration with multiple plans
- **Real-time Updates**: Server-sent events for job progress
- **Rate Limiting**: Built-in protection against abuse
- **Scalable Architecture**: DynamoDB + Step Functions for production

## 🏗️ Architecture

```
Client → Next.js API → DynamoDB → Step Functions → Groq/LLMs → Real-time Updates
```

- **Frontend**: Next.js 15 with TypeScript and Tailwind CSS
- **Backend**: Next.js API routes with Edge runtime
- **Database**: DynamoDB (production) / In-memory (development)
- **Orchestration**: AWS Step Functions with Lambda functions
- **Storage**: S3 for audio files (24h TTL)
- **Billing**: Stripe subscriptions with usage-based billing
- **Auth**: JWT tokens with bcrypt password hashing

## 📋 Prerequisites

- Node.js 18+ and npm
- AWS Account (for production deployment)
- Groq API key (for transcription)
- Stripe account (for billing)

## 🛠️ Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd youtube-chapter
npm install
```

### 2. Environment Setup

```bash
cd apps/web
cp .env.example .env.local
```

Edit `.env.local` with your API keys:

```env
# Required for transcription
GROQ_API_KEY=gsk_your_groq_api_key_here

# Required for production auth
JWT_SECRET=your-super-secure-jwt-secret

# Required for billing (optional for development)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

### 3. Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

### 4. Create Account & Test

1. Go to `/signup` to create an account
2. Sign in at `/signin`
3. Visit `/app` to generate chapters for a YouTube video
4. Check `/account` for usage and billing info

## 🔧 Configuration

### Storage Backend

**Development (default):**
```env
NEXT_PUBLIC_STORAGE=memory
```

**Production:**
```env
NEXT_PUBLIC_STORAGE=dynamodb
AWS_REGION=us-east-1
JOBS_TABLE=youtube-chapter-infra-prod-jobs
USERS_TABLE=youtube-chapter-infra-prod-users
```

### API Keys

Get your API keys from:
- **Groq**: [console.groq.com/keys](https://console.groq.com/keys)
- **Stripe**: [dashboard.stripe.com/apikeys](https://dashboard.stripe.com/apikeys)
- **YouTube** (optional): [console.developers.google.com](https://console.developers.google.com/)

## 🚀 Production Deployment

### 1. Infrastructure Deployment

```bash
# Install Serverless Framework
npm install -g serverless

# Deploy AWS infrastructure
npm run deploy
```

This creates:
- DynamoDB tables for users, jobs, videos, chapters, transcripts
- S3 bucket for audio storage
- Step Functions workflow
- Lambda functions for processing
- EventBridge for orchestration

### 2. Environment Variables

Set these in your hosting platform (Vercel, AWS, etc.):

```env
NEXT_PUBLIC_STORAGE=dynamodb
AWS_REGION=us-east-1
JWT_SECRET=your-production-jwt-secret
GROQ_API_KEY=your-groq-api-key
STRIPE_SECRET_KEY=sk_live_your-stripe-key
# ... other production variables
```

### 3. Stripe Setup

1. Create products and prices in Stripe Dashboard
2. Set up webhook endpoint: `https://yourdomain.com/api/billing/webhook`
3. Configure webhook events: `checkout.session.completed`, `customer.subscription.updated`, etc.
4. Update price IDs in environment variables

## 📊 Plans & Pricing

| Plan | Price | Minutes | Features |
|------|-------|---------|----------|
| Starter | $9/mo | 120 min | Basic chapters, email support |
| Creator | $29/mo | 400 min | Enhanced titles, priority processing |
| Pro | $99/mo | 1,600 min | API access, custom integrations |

- Overage: $0.05 per additional minute
- Max video length: 120 minutes
- All plans include unlimited videos

## 🔒 Security Features

- **Rate Limiting**: IP and user-based limits
- **Authentication**: JWT tokens with secure password hashing
- **Input Validation**: Comprehensive request validation
- **CORS Protection**: Configurable allowed origins
- **Webhook Verification**: Stripe webhook signature validation

## 📈 Monitoring & Observability

### Built-in Metrics
- Request rates and response times
- Error rates by endpoint
- Usage by plan and customer
- Billing and revenue metrics

### Recommended Integrations
- **Error Tracking**: Sentry
- **Performance**: DataDog or New Relic
- **Uptime**: Pingdom or UptimeRobot

## 🧪 Testing

```bash
# Run tests
npm test

# Run linting
npm run lint

# Type checking
npm run type-check
```

## 📚 API Documentation

### Authentication
```bash
# Sign up
POST /api/auth/signup
{
  "email": "<EMAIL>",
  "password": "securepassword"
}

# Sign in
POST /api/auth/signin
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

### Chapter Generation
```bash
# Generate chapters
POST /api/generate
Authorization: Bearer <jwt-token>
{
  "url": "https://www.youtube.com/watch?v=VIDEO_ID",
  "options": {
    "chapter_density": "auto",
    "title_style": "concise",
    "include_summaries": true
  }
}
```

### Usage & Billing
```bash
# Get usage
GET /api/usage
Authorization: Bearer <jwt-token>

# Create checkout session
POST /api/billing/create-checkout
Authorization: Bearer <jwt-token>
{
  "plan": "creator"
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- **Documentation**: Check this README and code comments
- **Issues**: Create a GitHub issue
- **Email**: <EMAIL>

## 🗺️ Roadmap

- [ ] Bulk video processing
- [ ] YouTube API integration for auto-upload
- [ ] Team accounts and shared allowances
- [ ] Multi-language support
- [ ] Custom chapter templates
- [ ] Analytics dashboard
- [ ] Mobile app

---

Built with ❤️ using Next.js, TypeScript, and AWS

- POST `/api/generate`: submit a URL; accepts optional `Idempotency-Key`, `Authorization`, and `x-plan`
- GET `/api/jobs/[jobId]`: fetch job status/results (no-store cache header)
- GET `/api/usage`: usage summary
- POST `/api/worker`: manually drain the in-memory event queue (auto-drained in dev)
- GET/DELETE `/api/dlq`: inspect/purge the in-memory DLQ

OpenAPI spec lives at `public/openapi.yaml`.
