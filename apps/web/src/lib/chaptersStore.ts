import type { Chapter } from "./types";

const backend = process.env.NEXT_PUBLIC_CHAPTERS_STORE || "memory";

const memory = new Map<string, Chapter[]>();

function memorySave(videoId: string, chapters: Chapter[]): void {
  memory.set(videoId, chapters);
}

export async function saveChapters(videoId: string, chapters: Chapter[]): Promise<void> {
  if (backend === "dynamodb") {
    const mod = await import("@/lib/chaptersStore.dynamo");
    return mod.saveChapters(videoId, chapters);
  }
  memorySave(videoId, chapters);
  return Promise.resolve();
}

export async function getChapters(videoId: string): Promise<Chapter[]> {
  if (backend === "dynamodb") {
    const mod = await import("@/lib/chaptersStore.dynamo");
    return mod.getChapters(videoId);
  }
  return memory.get(videoId) || [];
}


