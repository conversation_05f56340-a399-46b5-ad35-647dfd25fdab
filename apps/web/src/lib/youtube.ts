function parseIso8601Duration(iso: string): number {
  // Example: PT1H2M10S
  const match = /PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/.exec(iso);
  if (!match) return 0;
  const hours = parseInt(match[1] || "0", 10);
  const minutes = parseInt(match[2] || "0", 10);
  const seconds = parseInt(match[3] || "0", 10);
  return hours * 3600 + minutes * 60 + seconds;
}

export async function fetchYouTubeDurationSeconds(videoId: string): Promise<number | null> {
  const apiKey = process.env.YOUTUBE_API_KEY;
  if (!apiKey) return null;
  // Simple in-memory cache with 10 min TTL
  const now = Date.now();
  (globalThis as any).__ytCache = (globalThis as any).__ytCache || new Map<string, { s: number; e: number }>();
  const cache: Map<string, { s: number; e: number }> = (globalThis as any).__ytCache;
  const hit = cache.get(videoId);
  if (hit && hit.e > now) return hit.s;
  const url = `https://www.googleapis.com/youtube/v3/videos?part=contentDetails&id=${encodeURIComponent(
    videoId
  )}&key=${encodeURIComponent(apiKey)}`;
  try {
    const res = await fetch(url, { next: { revalidate: 60 } });
    if (!res.ok) return null;
    const data = (await res.json()) as any;
    const item = data?.items?.[0];
    const iso: string | undefined = item?.contentDetails?.duration;
    if (!iso) return null;
    const seconds = parseIso8601Duration(iso);
    cache.set(videoId, { s: seconds, e: now + 10 * 60 * 1000 });
    return seconds;
  } catch {
    return null;
  }
}


