import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, BatchWriteCommand, QueryCommand } from "@aws-sdk/lib-dynamodb";
import type { Chapter } from "./types";

const REGION = process.env.AWS_REGION || "us-east-1";
const TABLE = process.env.CHAPTERS_TABLE || "chapters-dev";
const ENDPOINT = process.env.DYNAMODB_ENDPOINT;

const client = new DynamoDBClient({
  region: REGION,
  endpoint: ENDPOINT,
  credentials: ENDPOINT ? { accessKeyId: "local", secretAccessKey: "local" } : undefined,
});
const ddb = DynamoDBDocumentClient.from(client);

export async function saveChapters(videoId: string, chapters: Chapter[]): Promise<void> {
  if (!chapters.length) return;
  const Requests = chapters.map((c, idx) => ({
    PutRequest: {
      Item: {
        video_id: videoId,
        c: `c#${String(idx).padStart(3, "0")}`,
        start: c.start,
        end: c.end,
        duration_sec: c.duration_sec,
        title: c.title,
        summary: c.summary,
      },
    },
  }));
  await ddb.send(new BatchWriteCommand({ RequestItems: { [TABLE]: Requests } }));
}

export async function getChapters(videoId: string): Promise<Chapter[]> {
  const res = await ddb.send(new QueryCommand({
    TableName: TABLE,
    KeyConditionExpression: "video_id = :v",
    ExpressionAttributeValues: { ":v": videoId },
  }));
  const items = (res.Items as any[]) || [];
  items.sort((a, b) => (a.c < b.c ? -1 : 1));
  return items.map((i) => ({ start: i.start, end: i.end, duration_sec: i.duration_sec, title: i.title, summary: i.summary }));
}


