import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, GetCommand, PutCommand } from "@aws-sdk/lib-dynamodb";
import type { TranscriptSegment } from "./transcribe";

export type CachedTranscript = {
  video_id: string;
  segments: TranscriptSegment[];
  language?: string;
  expires_at: number; // epoch ms
};

const REGION = process.env.AWS_REGION || "us-east-1";
const TABLE = process.env.TRANSCRIPTS_TABLE || "transcripts-dev";
const ENDPOINT = process.env.DYNAMODB_ENDPOINT;

const client = new DynamoDBClient({
  region: REGION,
  endpoint: ENDPOINT,
  credentials: ENDPOINT ? { accessKeyId: "local", secretAccessKey: "local" } : undefined,
});
const ddb = DynamoDBDocumentClient.from(client);

export async function getCachedTranscript(videoId: string): Promise<CachedTranscript | null> {
  const res = await ddb.send(new GetCommand({ TableName: TABLE, Key: { video_id: videoId } }));
  const item = res.Item as CachedTranscript | undefined;
  if (!item) return null;
  if (item.expires_at <= Date.now()) return null;
  return item;
}

export async function setCachedTranscript(
  videoId: string,
  segments: TranscriptSegment[],
  language?: string,
  ttlMs: number = 24 * 60 * 60 * 1000
): Promise<void> {
  const expiresAt = Date.now() + Math.max(60_000, ttlMs);
  await ddb.send(
    new PutCommand({
      TableName: TABLE,
      Item: {
        video_id: videoId,
        segments,
        language,
        expires_at: expiresAt,
      },
    })
  );
}


