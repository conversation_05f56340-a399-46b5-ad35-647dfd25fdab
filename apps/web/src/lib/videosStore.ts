const backend = process.env.NEXT_PUBLIC_VIDEOS_STORE || "memory";

export type VideoRecord = {
  video_id: string;
  duration_sec: number;
  language?: string;
  created_at: string;
  customer_id?: string;
};

const memory = new Map<string, VideoRecord>();

function memorySave(video: VideoRecord): void {
  memory.set(video.video_id, video);
}

export async function saveVideo(video: VideoRecord): Promise<void> {
  if (backend === "dynamodb") {
    const mod = await import("@/lib/videosStore.dynamo");
    return mod.saveVideo(video);
  }
  memorySave(video);
  return Promise.resolve();
}

export async function listVideos(customerId?: string, limit = 50): Promise<VideoRecord[]> {
  if (backend === "dynamodb") {
    const mod = await import("@/lib/videosStore.dynamo");
    return mod.listVideos(customerId, limit);
  }
  const all = Array.from(memory.values());
  const filtered = customerId ? all.filter((v) => v.customer_id === customerId) : all;
  filtered.sort((a, b) => (a.created_at < b.created_at ? 1 : -1));
  return filtered.slice(0, limit);
}


