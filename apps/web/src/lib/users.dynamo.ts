import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, PutCommand, GetCommand, UpdateCommand, DeleteCommand, QueryCommand, ScanCommand } from "@aws-sdk/lib-dynamodb";
import { User } from '@/lib/auth';

const client = new DynamoDBClient({
  region: process.env.AWS_REGION || "us-east-1",
  ...(process.env.NODE_ENV === "development" && {
    endpoint: "http://localhost:8000",
    credentials: {
      accessKeyId: "dummy",
      secretAccessKey: "dummy",
    },
  }),
});

const docClient = DynamoDBDocumentClient.from(client);
const USERS_TABLE = process.env.USERS_TABLE || "youtube-chapter-infra-dev-users";

export async function getUserById(id: string): Promise<User | null> {
  try {
    const result = await docClient.send(new GetCommand({
      TableName: USERS_TABLE,
      Key: { id },
    }));
    
    return result.Item as User || null;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
}

export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const result = await docClient.send(new QueryCommand({
      TableName: USERS_TABLE,
      IndexName: "gsi_email",
      KeyConditionExpression: "email = :email",
      ExpressionAttributeValues: {
        ":email": email.toLowerCase(),
      },
    }));
    
    return result.Items?.[0] as User || null;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
}

export async function createUser(user: User): Promise<User> {
  try {
    await docClient.send(new PutCommand({
      TableName: USERS_TABLE,
      Item: user,
      ConditionExpression: "attribute_not_exists(id)",
    }));
    
    return user;
  } catch (error) {
    console.error('Error creating user:', error);
    throw new Error('Failed to create user');
  }
}

export async function updateUser(id: string, updates: Partial<User>): Promise<User | null> {
  try {
    // Build update expression
    const updateExpressions: string[] = [];
    const expressionAttributeNames: Record<string, string> = {};
    const expressionAttributeValues: Record<string, any> = {};
    
    Object.entries(updates).forEach(([key, value], index) => {
      if (key !== 'id') { // Don't update the primary key
        const attrName = `#attr${index}`;
        const attrValue = `:val${index}`;
        updateExpressions.push(`${attrName} = ${attrValue}`);
        expressionAttributeNames[attrName] = key;
        expressionAttributeValues[attrValue] = value;
      }
    });
    
    if (updateExpressions.length === 0) {
      return await getUserById(id);
    }
    
    const result = await docClient.send(new UpdateCommand({
      TableName: USERS_TABLE,
      Key: { id },
      UpdateExpression: `SET ${updateExpressions.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: "ALL_NEW",
    }));
    
    return result.Attributes as User || null;
  } catch (error) {
    console.error('Error updating user:', error);
    return null;
  }
}

export async function updateUserLastLogin(id: string): Promise<void> {
  try {
    await docClient.send(new UpdateCommand({
      TableName: USERS_TABLE,
      Key: { id },
      UpdateExpression: "SET last_login = :timestamp",
      ExpressionAttributeValues: {
        ":timestamp": new Date().toISOString(),
      },
    }));
  } catch (error) {
    console.error('Error updating user last login:', error);
  }
}

export async function deleteUser(id: string): Promise<boolean> {
  try {
    await docClient.send(new DeleteCommand({
      TableName: USERS_TABLE,
      Key: { id },
    }));
    
    return true;
  } catch (error) {
    console.error('Error deleting user:', error);
    return false;
  }
}

export async function getUserByStripeCustomerId(stripeCustomerId: string): Promise<User | null> {
  try {
    const result = await docClient.send(new ScanCommand({
      TableName: USERS_TABLE,
      FilterExpression: "stripe_customer_id = :stripe_customer_id",
      ExpressionAttributeValues: {
        ":stripe_customer_id": stripeCustomerId,
      },
      Limit: 1,
    }));

    return result.Items?.[0] as User || null;
  } catch (error) {
    console.error('Error getting user by Stripe customer ID:', error);
    return null;
  }
}

export async function listUsers(limit = 50): Promise<User[]> {
  try {
    const result = await docClient.send(new ScanCommand({
      TableName: USERS_TABLE,
      Limit: limit,
    }));

    const users = (result.Items as User[]) || [];
    users.sort((a, b) => (a.created_at < b.created_at ? 1 : -1));

    return users;
  } catch (error) {
    console.error('Error listing users:', error);
    return [];
  }
}
