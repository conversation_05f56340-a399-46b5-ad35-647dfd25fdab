type Plan = "starter" | "creator" | "pro";

const PLAN_ALLOWANCE_MIN = {
  starter: 120,
  creator: 400,
  pro: 1600,
} as const satisfies Record<Plan, number>;

const wallet = new Map<string, number>(); // customer_id -> minutes_used (this period)

export function getPlanAllowance(plan: Plan): number {
  return PLAN_ALLOWANCE_MIN[plan];
}

export function getUsageMinutes(customerId: string): number {
  return wallet.get(customerId) ?? 0;
}

export function canConsume(customerId: string, plan: Plan, minutes: number): boolean {
  const used = getUsageMinutes(customerId);
  return used + minutes <= getPlanAllowance(plan);
}

export function recordUsage(customerId: string, minutes: number): void {
  const used = getUsageMinutes(customerId);
  wallet.set(customerId, used + minutes);
}

export function getRemainingMinutes(customerId: string, plan: Plan): number {
  return Math.max(0, getPlanAllowance(plan) - getUsageMinutes(customerId));
}

export type UsageSummary = {
  customer_id: string;
  plan: Plan;
  used: number;
  allowance: number;
  remaining: number;
};

export function getUsageSummary(customerId: string, plan: Plan): UsageSummary {
  const used = getUsageMinutes(customerId);
  const allowance = getPlanAllowance(plan);
  return {
    customer_id: customerId,
    plan,
    used,
    allowance,
    remaining: Math.max(0, allowance - used),
  };
}


