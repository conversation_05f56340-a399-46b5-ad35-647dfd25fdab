import ytdl from "ytdl-core";

export async function fetchYouTubeAudioAsFile(url: string): Promise<File> {
  const info = await ytdl.getInfo(url);
  const format = ytdl.chooseFormat(info.formats, { quality: "highestaudio" });
  if (!format?.url) throw new Error("No audio format available");
  const res = await fetch(format.url);
  if (!res.ok) throw new Error(`Failed to fetch audio from YouTube (${res.status})`);
  const buf = await res.arrayBuffer();
  const contentType = res.headers.get("content-type") || "audio/mpeg";
  const filename = `yt_audio_${info.videoDetails.videoId}.bin`;
  return new File([buf], filename, { type: contentType });
}


