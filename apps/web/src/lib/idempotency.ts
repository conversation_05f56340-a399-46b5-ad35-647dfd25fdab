const idempotencyMap = new Map<string, string>(); // key -> job_id

export function makeIdempotencyKey(parts: Array<string | number | boolean>): string {
  const content = parts.join("|");
  // Simple stable hash; avoid crypto for Edge portability
  let hash = 0;
  for (let i = 0; i < content.length; i += 1) {
    const chr = content.charCodeAt(i);
    hash = (hash << 5) - hash + chr;
    hash |= 0; // 32-bit
  }
  return `idem_${Math.abs(hash)}`;
}

export function rememberIdempotency(key: string, jobId: string): void {
  idempotencyMap.set(key, jobId);
}

export function getIdempotentJobId(key: string): string | undefined {
  return idempotencyMap.get(key);
}


