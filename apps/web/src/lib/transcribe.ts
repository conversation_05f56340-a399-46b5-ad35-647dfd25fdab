// Groq Whisper transcription helper (best-effort; optional at runtime)
// Requires: GROQ_API_KEY and a direct-accessible audio URL (e.g., mp3/mp4/m4a)

export type TranscriptSegment = {
  start: number; // seconds
  end: number; // seconds
  text: string;
};

export type TranscriptionResult = {
  text: string;
  segments?: TranscriptSegment[];
  language?: string;
};

export async function fetchAudioAsFile(audioUrl: string): Promise<File> {
  const res = await fetch(audioUrl);
  if (!res.ok) throw new Error(`Failed to fetch audio (${res.status})`);
  const buf = await res.arrayBuffer();
  const contentType = res.headers.get("content-type") || "audio/mpeg";
  const filename = `audio_${Date.now()}.bin`;
  return new File([buf], filename, { type: contentType });
}

export async function transcribeWithGroqFromUrl(audioUrl: string): Promise<TranscriptionResult | null> {
  const apiKey = process.env.GROQ_API_KEY;
  if (!apiKey) return null;
  try {
    const file = await fetchAudioAsFile(audioUrl);
    // Dynamic import supported in server context
    const mod = await import("groq-sdk");
    const Groq = (mod as any).default || (mod as any);
    const groq = new Groq({ apiKey });
    // Use Whisper-large-v3 (or v2); ask for verbose JSON if supported
    const resp = await (groq as any).audio.transcriptions.create({
      file,
      model: "whisper-large-v3",
      response_format: "verbose_json",
    });
    // Try to normalize response
    const text: string = resp?.text || "";
    const segments: TranscriptSegment[] | undefined = resp?.segments?.map((s: any) => ({
      start: Number(s.start ?? 0),
      end: Number(s.end ?? 0),
      text: String(s.text ?? ""),
    }));
    const language: string | undefined = resp?.language || resp?.detected_language || undefined;
    return { text, segments, language };
  } catch {
    return null;
  }
}


