import type { Chapter } from "./types";
import { secondsToHms } from "./time";
import type { TranscriptSegment } from "./transcribe";

export type ChapterDensity = "auto" | "sparse" | "dense";

function clamp(value: number, min: number, max: number): number {
  return Math.max(min, Math.min(max, value));
}

export function computeChapterCount(durationMinutes: number, density: ChapterDensity): number {
  const base = Math.ceil(durationMinutes / 6);
  const densityMultiplier = density === "sparse" ? 0.75 : density === "dense" ? 1.25 : 1.0;
  const n = Math.round(base * densityMultiplier);
  return clamp(n, 6, 25);
}

export function segmentByLength(
  durationSeconds: number,
  density: ChapterDensity = "auto"
): Chapter[] {
  const durationMinutes = Math.ceil(durationSeconds / 60);
  const numChapters = computeChapterCount(durationMinutes, density);
  if (numChapters <= 0) return [];

  const segmentLength = Math.floor(durationSeconds / numChapters);
  const chapters: Chapter[] = [];
  for (let i = 0; i < numChapters; i += 1) {
    const startSec = i * segmentLength;
    const isLast = i === numChapters - 1;
    const endSec = isLast ? durationSeconds : (i + 1) * segmentLength;
    const chapter: Chapter = {
      start: secondsToHms(startSec),
      end: secondsToHms(endSec),
      duration_sec: Math.max(0, endSec - startSec),
      title: `Section ${i + 1}`,
    };
    chapters.push(chapter);
  }
  return chapters;
}

export function segmentByTranscript(
  segments: TranscriptSegment[],
  targetCount: number
): Chapter[] {
  if (!segments.length) return [];
  const totalSeconds = Math.max(1, Math.floor(segments[segments.length - 1].end - segments[0].start));
  const buckets: { start: number; end: number }[] = [];
  const quota = totalSeconds / Math.max(1, targetCount);
  let bucketStart = segments[0].start;
  let acc = 0;
  let lastEnd = bucketStart;
  for (const s of segments) {
    const dur = Math.max(0, s.end - (lastEnd || s.start));
    acc += dur;
    lastEnd = s.end;
    if (acc >= quota && buckets.length < targetCount - 1) {
      buckets.push({ start: bucketStart, end: s.end });
      bucketStart = s.end;
      acc = 0;
    }
  }
  buckets.push({ start: bucketStart, end: lastEnd });
  return buckets.map((b, i) => ({
    start: secondsToHms(Math.max(0, Math.floor(b.start))),
    end: secondsToHms(Math.max(0, Math.floor(b.end))),
    duration_sec: Math.max(0, Math.floor(b.end - b.start)),
    title: `Section ${i + 1}`,
  }));
}


