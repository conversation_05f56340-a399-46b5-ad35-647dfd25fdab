import type { JobRecord } from "./types";

type Client = {
  customerId: string;
  push: (chunk: string) => void;
  close: () => void;
};

const clients = new Set<Client>();

export function registerClient(customerId: string, push: (chunk: string) => void, close: () => void): () => void {
  const client: Client = { customerId, push, close };
  clients.add(client);
  return () => {
    try {
      clients.delete(client);
      client.close();
    } catch {
      // ignore
    }
  };
}

function sseEvent(event: string, data: unknown): string {
  return `event: ${event}\n` + `data: ${JSON.stringify(data)}\n\n`;
}

export function broadcastJobCompleted(job: JobRecord): void {
  const payload = { job_id: job.job_id, video_id: job.video_id, status: job.status, minutes_billed: job.minutes_billed };
  const msg = sseEvent("job.completed", payload);
  for (const c of clients) {
    if (job.customer_id && c.customerId === job.customer_id) {
      try {
        c.push(msg);
      } catch {
        // ignore broken pipe
      }
    }
  }
}


