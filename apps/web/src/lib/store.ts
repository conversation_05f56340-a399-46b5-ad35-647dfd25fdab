import { Chapter, JobRecord, JobStatus } from "./types";

const jobStore = (globalThis as any).jobStore || new Map<string, JobRecord>();
if (process.env.NODE_ENV !== "production") (globalThis as any).jobStore = jobStore;

export function saveJob(record: JobRecord): void {
  jobStore.set(record.job_id, record);
}

export function getJob(jobId: string): JobRecord | undefined {
  return jobStore.get(jobId);
}

export function updateJobStatus(jobId: string, status: JobStatus): void {
  const current = jobStore.get(jobId);
  if (!current) return;
  jobStore.set(jobId, { ...current, status });
}

export function completeJob(
  jobId: string,
  chapters: Chapter[],
  minutesBilled: number
): void {
  const current = jobStore.get(jobId);
  if (!current) return;
  jobStore.set(jobId, {
    ...current,
    status: "completed",
    chapters,
    minutes_billed: minutesBilled,
    completed_at: new Date().toISOString(),
  });
}

export function listJobs(limit = 50): JobRecord[] {
  const all = Array.from(jobStore.values()) as JobRecord[];
  all.sort((a, b) => (a.created_at < b.created_at ? 1 : -1));
  return all.slice(0, limit);
}


