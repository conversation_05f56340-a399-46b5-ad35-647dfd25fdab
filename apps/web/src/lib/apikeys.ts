export type Plan = "starter" | "creator" | "pro";

export type ApiKeyRecord = {
  key: string;
  customer_id: string;
  plan: Plan;
};

function loadApiKeys(): ApiKeyRecord[] {
  const json = process.env.API_KEYS_JSON;
  if (!json) return [];
  try {
    const arr = JSON.parse(json) as ApiKeyRecord[];
    if (Array.isArray(arr)) return arr.filter((r) => r && r.key && r.customer_id && r.plan);
    return [];
  } catch {
    return [];
  }
}

const cachedKeys = loadApiKeys();

export function lookupApiKey(token: string): ApiKeyRecord | null {
  if (!token) return null;
  // Constant-time-ish compare not critical here; tokens should be sufficiently random
  const found = cachedKeys.find((r) => r.key === token);
  return found || null;
}


