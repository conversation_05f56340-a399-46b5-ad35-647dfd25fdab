import type { Chapter, JobRecord, JobStatus } from "./types";
import * as memory from "./store";
import * as dynamo from "./storage.dynamo";
import { listJobs as listJobsMemory } from "./store";

export interface StorageProvider {
  saveJob(record: JobRecord): void | Promise<void>;
  getJob(jobId: string): JobRecord | undefined | Promise<JobRecord | undefined>;
  updateJobStatus(jobId: string, status: JobStatus): void | Promise<void>;
  completeJob(jobId: string, chapters: Chapter[], minutesBilled: number): void | Promise<void>;
  listJobs?(options?: { limit?: number; customerId?: string; nextToken?: string }):
    | JobRecord[]
    | Promise<JobRecord[]>
    | { jobs: JobRecord[]; nextToken?: string }
    | Promise<{ jobs: JobRecord[]; nextToken?: string }>;
}

function getProvider(): StorageProvider {
  const backend = "memory"; // Force memory for local dev
  switch (backend) {
    case "memory":
    default:
      return {
        saveJob: memory.saveJob,
        getJob: memory.getJob,
        updateJobStatus: memory.updateJobStatus,
        completeJob: memory.completeJob,
        listJobs: ({ limit = 50 }: { limit?: number } = {}) => listJobsMemory(limit),
      };
    case "dynamodb":
      return {
        saveJob: dynamo.saveJob,
        getJob: dynamo.getJob,
        updateJobStatus: dynamo.updateJobStatus,
        completeJob: dynamo.completeJob,
        listJobs: dynamo.listJobs,
      };
  }
}

const provider = getProvider();

export const saveJob = provider.saveJob.bind(provider) as StorageProvider["saveJob"];
export const getJob = provider.getJob.bind(provider) as StorageProvider["getJob"];
export const updateJobStatus = provider.updateJobStatus.bind(provider) as StorageProvider["updateJobStatus"];
export const completeJob = provider.completeJob.bind(provider) as StorageProvider["completeJob"];


