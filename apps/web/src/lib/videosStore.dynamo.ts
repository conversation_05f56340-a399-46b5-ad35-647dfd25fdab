import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, PutCommand, QueryCommand, ScanCommand } from "@aws-sdk/lib-dynamodb";
import type { VideoRecord } from "./videosStore";

const REGION = process.env.AWS_REGION || "us-east-1";
const TABLE = process.env.VIDEOS_TABLE || "videos-dev";
const ENDPOINT = process.env.DYNAMODB_ENDPOINT;

const client = new DynamoDBClient({
  region: REGION,
  endpoint: ENDPOINT,
  credentials: ENDPOINT ? { accessKeyId: "local", secretAccessKey: "local" } : undefined,
});
const ddb = DynamoDBDocumentClient.from(client);

export async function saveVideo(video: VideoRecord): Promise<void> {
  await ddb.send(new PutCommand({ TableName: TABLE, Item: video }));
}

export async function listVideos(customerId?: string, limit = 50): Promise<VideoRecord[]> {
  if (customerId) {
    // If table has GSI for customer_id + created_at, use it; otherwise scan
    try {
      const res = await ddb.send(new QueryCommand({
        TableName: TABLE,
        IndexName: "gsi_customer_created",
        KeyConditionExpression: "customer_id = :c",
        ExpressionAttributeValues: { ":c": customerId },
        ScanIndexForward: false,
        Limit: limit,
      }));
      return (res.Items as VideoRecord[]) || [];
    } catch {
      // ignore and scan instead
    }
  }
  const res = await ddb.send(new ScanCommand({ TableName: TABLE, Limit: limit }));
  return (res.Items as VideoRecord[]) || [];
}


