export type UsageRecord = {
  customer_id: string;
  minutes: number;
  timestamp: number; // epoch ms
  job_id: string;
};

const records: UsageRecord[] = [];

export async function postUsageRecord(record: UsageRecord): Promise<void> {
  records.push(record);
  const apiKey = process.env.STRIPE_SECRET_KEY;
  const meterId = process.env.STRIPE_METER_ID; // subscription_item id for metered usage
  if (!apiKey || !meterId) return; // remain noop if not configured

  try {
    // Using Stripe Usage Records API
    const stripeMod = await import("stripe");
    const stripe = new stripeMod.default(apiKey, { apiVersion: "2024-06-20" } as any);
    // Post one unit per minute with the job_id as idempotency key
    await (stripe as any).subscriptionItems.createUsageRecord(meterId, {
      quantity: Math.max(1, Math.ceil(record.minutes)),
      timestamp: Math.floor(record.timestamp / 1000),
      action: "increment",
    }, { idempotencyKey: record.job_id });
  } catch {
    // ignore errors in demo
  }
}

export function listUsageRecords(): UsageRecord[] {
  return [...records];
}

export function clearUsageRecords(): number {
  const n = records.length;
  records.length = 0;
  return n;
}


