import type { AuthContext } from "./auth";

export type BusEvent =
  | {
      type: "VideoSubmitted";
      payload: {
        jobId: string;
        videoId: string;
        estimatedMinutes: number;
        optionsJson: string;
        auth: AuthContext;
      };
    };

type EventHandler = (event: BusEvent) => Promise<void>;

type Queued = { event: BusEvent; attempts: number };
const queue: Queued[] = [];
const dlq: Queued[] = [];
let handler: EventHandler | null = null;
const MAX_ATTEMPTS = 3;

export function setEventHandler(fn: EventHandler): void {
  handler = fn;
}

export function publish(event: BusEvent): number {
  queue.push({ event, attempts: 0 });
  return queue.length;
}

export function getDlq(): ReadonlyArray<Queued> {
  return dlq;
}

export function purgeDlq(): number {
  const n = dlq.length;
  dlq.length = 0;
  return n;
}

export async function drainQueue(): Promise<void> {
  while (queue.length > 0) {
    const item = queue.shift()!;
    if (!handler) continue;
    try {
      await handler(item.event);
    } catch {
      item.attempts += 1;
      if (item.attempts >= MAX_ATTEMPTS) {
        dlq.push(item);
      } else {
        queue.push(item);
      }
    }
  }
}


