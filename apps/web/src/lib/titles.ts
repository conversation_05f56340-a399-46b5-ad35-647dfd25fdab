import type { Chapter } from "./types";

function toOrdinal(n: number): string {
  const s = ["th", "st", "nd", "rd"], v = n % 100;
  return n + (s[(v - 20) % 10] || s[v] || s[0]);
}

export type TitleStyle = "concise" | "descriptive";

export function refineChapterTitles(
  chapters: Chapter[],
  style: TitleStyle = "concise"
): Chapter[] {
  return chapters.map((c, i) => {
    const idx = i + 1;
    const base = style === "descriptive" ? `Chapter ${toOrdinal(idx)}` : `Section ${idx}`;
    return { ...c, title: c.title && c.title.startsWith("Section ") ? base : c.title || base };
  });
}


