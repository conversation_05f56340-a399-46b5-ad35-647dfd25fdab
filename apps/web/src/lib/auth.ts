export type AuthContext = {
  customer_id: string;
  plan: "starter" | "creator" | "pro";
};

import { lookupApiKey } from "@/lib/apikeys";

export function parseAuth(headers: Headers): AuthContext {
  const auth = headers.get("authorization") || headers.get("Authorization");
  // API key path
  if (auth && auth.startsWith("Bearer ")) {
    const token = auth.slice("Bearer ".length).trim();
    if (token) {
      const rec = lookupApiKey(token);
      if (rec) return { customer_id: rec.customer_id, plan: rec.plan };
      // fallback: derive customer id from token (dev only)
      const planHeader = (headers.get("x-plan") || "starter").toLowerCase();
      const plan = (planHeader === "creator" || planHeader === "pro") ? (planHeader as AuthContext["plan"]) : ("starter" as const);
      return { customer_id: `cus_${token.slice(0, 12)}`, plan };
    }
  }
  // Fallback demo identity if no auth
  const planHeader = (headers.get("x-plan") || "starter").toLowerCase();
  const plan = (planHeader === "creator" || planHeader === "pro") ? (planHeader as AuthContext["plan"]) : ("starter" as const);
  return { customer_id: "cus_demo", plan };
}


