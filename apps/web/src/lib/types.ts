export type Chapter = {
  start: string; // HH:MM:SS
  end: string; // HH:MM:SS
  duration_sec: number;
  title: string;
  summary?: string;
};

export type JobStatus = "queued" | "processing" | "completed" | "failed";

export type JobRecord = {
  job_id: string;
  video_id: string;
  status: JobStatus;
  estimated_minutes: number;
  billing_preview_minutes: number;
  minutes_billed?: number;
  chapters?: Chapter[];
  error?: string;
  created_at: string; // ISO
  completed_at?: string; // ISO
  customer_id?: string;
};


