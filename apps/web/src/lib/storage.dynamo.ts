import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, GetCommand, PutCommand, UpdateCommand, ScanCommand, QueryCommand } from "@aws-sdk/lib-dynamodb";
import type { Chapter, JobRecord, JobStatus } from "./types";

const REGION = process.env.AWS_REGION || "us-east-1";
const TABLE = process.env.JOBS_TABLE || "jobs-dev";
const ENDPOINT = process.env.DYNAMODB_ENDPOINT;

const client = new DynamoDBClient({
  region: REGION,
  endpoint: ENDPOINT,
  // Local DynamoDB requires credentials to be set, but any values work
  credentials: ENDPOINT
    ? { accessKeyId: "local", secretAccessKey: "local" }
    : undefined,
});
const ddb = DynamoDBDocumentClient.from(client);

export async function saveJob(record: JobRecord): Promise<void> {
  try {
    await ddb.send(
      new PutCommand({
        TableName: TABLE,
        Item: record,
        ConditionExpression: "attribute_not_exists(job_id)",
      })
    );
  } catch (err: any) {
    if (err?.name === "ConditionalCheckFailedException") {
      // Job already exists (idempotent create) — safe to ignore
      return;
    }
    throw err;
  }
}

export async function getJob(jobId: string): Promise<JobRecord | undefined> {
  const res = await ddb.send(
    new GetCommand({
      TableName: TABLE,
      Key: { job_id: jobId },
    })
  );
  return (res.Item as JobRecord) || undefined;
}

export async function updateJobStatus(jobId: string, status: JobStatus): Promise<void> {
  await ddb.send(
    new UpdateCommand({
      TableName: TABLE,
      Key: { job_id: jobId },
      UpdateExpression: "SET #s = :s",
      ExpressionAttributeNames: { "#s": "status" },
      ExpressionAttributeValues: { ":s": status },
    })
  );
}

export async function completeJob(jobId: string, chapters: Chapter[], minutesBilled: number): Promise<void> {
  await ddb.send(
    new UpdateCommand({
      TableName: TABLE,
      Key: { job_id: jobId },
      UpdateExpression: "SET #s = :s, chapters = :c, minutes_billed = :m, completed_at = :t",
      ExpressionAttributeNames: { "#s": "status" },
      ExpressionAttributeValues: {
        ":s": "completed",
        ":c": chapters,
        ":m": minutesBilled,
        ":t": new Date().toISOString(),
      },
    })
  );
}

export async function listJobs(options: { limit?: number; customerId?: string; nextToken?: string } = {}): Promise<{
  jobs: JobRecord[];
  nextToken?: string;
}> {
  const { limit = 50, customerId, nextToken } = options;
  if (customerId) {
    const res = await ddb.send(
      new QueryCommand({
        TableName: TABLE,
        IndexName: "gsi_customer_created",
        KeyConditionExpression: "customer_id = :c",
        ExpressionAttributeValues: { ":c": customerId },
        ScanIndexForward: false,
        Limit: limit,
        ExclusiveStartKey: nextToken ? (JSON.parse(Buffer.from(nextToken, "base64").toString("utf8")) as any) : undefined,
      })
    );
    const items = (res.Items as JobRecord[]) || [];
    const token = res.LastEvaluatedKey ? Buffer.from(JSON.stringify(res.LastEvaluatedKey)).toString("base64") : undefined;
    return { jobs: items, nextToken: token };
  }
  const res = await ddb.send(new ScanCommand({ TableName: TABLE, Limit: limit }));
  const items = (res.Items as JobRecord[]) || [];
  return { jobs: items };
}


