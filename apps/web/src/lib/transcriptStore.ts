import type { TranscriptSegment } from "./transcribe";

export type CachedTranscript = {
  video_id: string;
  segments: TranscriptSegment[];
  language?: string;
  expires_at: number; // epoch ms
};

const DEFAULT_TTL_MS = 24 * 60 * 60 * 1000;
const backend = process.env.NEXT_PUBLIC_TRANSCRIPT_STORE || "memory";
const memoryStore = new Map<string, CachedTranscript>();

function memoryGet(videoId: string): CachedTranscript | null {
  const rec = memoryStore.get(videoId);
  if (!rec) return null;
  if (rec.expires_at <= Date.now()) {
    memoryStore.delete(videoId);
    return null;
  }
  return rec;
}

function memorySet(
  videoId: string,
  segments: TranscriptSegment[],
  language?: string,
  ttlMs: number = DEFAULT_TTL_MS
): void {
  memoryStore.set(videoId, {
    video_id: videoId,
    segments,
    language,
    expires_at: Date.now() + Math.max(60_000, ttlMs),
  });
}

// Optional Dynamo-backed overrides (dynamic import to satisfy lint)
if (backend === "dynamodb") {
  // Re-export functions from the dynamo module
  // Using dynamic import pattern supported on server
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  (async () => {
    const dyn = await import("@/lib/transcriptStore.dynamo");
    // Shadow functions by assigning to globals used by our exports
    (globalThis as any).__getCachedTranscript = dyn.getCachedTranscript;
    (globalThis as any).__setCachedTranscript = dyn.setCachedTranscript;
  })();
}

// Exported functions delegate to override if present
export function getCachedTranscript(videoId: string): CachedTranscript | null | Promise<CachedTranscript | null> {
  const dyn = (globalThis as any).__getCachedTranscript;
  return dyn ? dyn(videoId) : memoryGet(videoId);
}
export function setCachedTranscript(
  videoId: string,
  segments: TranscriptSegment[],
  language?: string,
  ttlMs: number = DEFAULT_TTL_MS
): void | Promise<void> {
  const dyn = (globalThis as any).__setCachedTranscript;
  return dyn ? dyn(videoId, segments, language, ttlMs) : memorySet(videoId, segments, language, ttlMs);
}


