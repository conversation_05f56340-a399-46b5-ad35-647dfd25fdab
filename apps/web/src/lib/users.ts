import { User } from '@/lib/auth';
import { nanoid } from 'nanoid';

// In-memory store for development (will be replaced with DynamoDB in production)
const usersStore = (globalThis as any).usersStore || new Map<string, User>();
if (process.env.NODE_ENV !== "production") (globalThis as any).usersStore = usersStore;

// Email to user ID mapping for quick lookups
const emailToIdStore = (globalThis as any).emailToIdStore || new Map<string, string>();
if (process.env.NODE_ENV !== "production") (globalThis as any).emailToIdStore = emailToIdStore;

export async function getUserById(id: string): Promise<User | null> {
  const backend = process.env.NEXT_PUBLIC_STORAGE || "memory";
  
  if (backend === "dynamodb") {
    const mod = await import("@/lib/users.dynamo");
    return mod.getUserById(id);
  }
  
  return usersStore.get(id) || null;
}

export async function getUserByEmail(email: string): Promise<User | null> {
  const backend = process.env.NEXT_PUBLIC_STORAGE || "memory";
  
  if (backend === "dynamodb") {
    const mod = await import("@/lib/users.dynamo");
    return mod.getUserByEmail(email);
  }
  
  const userId = emailToIdStore.get(email.toLowerCase());
  if (!userId) return null;
  
  return usersStore.get(userId) || null;
}

export async function createUser(userData: {
  email: string;
  password_hash: string;
  plan: "starter" | "creator" | "pro";
}): Promise<User> {
  const backend = process.env.NEXT_PUBLIC_STORAGE || "memory";
  
  const user: User = {
    id: `user_${nanoid()}`,
    email: userData.email.toLowerCase(),
    password_hash: userData.password_hash,
    customer_id: `cus_${nanoid()}`,
    plan: userData.plan,
    created_at: new Date().toISOString(),
    email_verified: false,
  };
  
  if (backend === "dynamodb") {
    const mod = await import("@/lib/users.dynamo");
    return mod.createUser(user);
  }
  
  // Memory storage
  usersStore.set(user.id, user);
  emailToIdStore.set(user.email, user.id);
  
  return user;
}

export async function updateUser(id: string, updates: Partial<User>): Promise<User | null> {
  const backend = process.env.NEXT_PUBLIC_STORAGE || "memory";
  
  if (backend === "dynamodb") {
    const mod = await import("@/lib/users.dynamo");
    return mod.updateUser(id, updates);
  }
  
  const existing = usersStore.get(id);
  if (!existing) return null;
  
  const updated = { ...existing, ...updates };
  usersStore.set(id, updated);
  
  // Update email mapping if email changed
  if (updates.email && updates.email !== existing.email) {
    emailToIdStore.delete(existing.email);
    emailToIdStore.set(updates.email.toLowerCase(), id);
  }
  
  return updated;
}

export async function updateUserLastLogin(id: string): Promise<void> {
  const backend = process.env.NEXT_PUBLIC_STORAGE || "memory";
  
  if (backend === "dynamodb") {
    const mod = await import("@/lib/users.dynamo");
    return mod.updateUserLastLogin(id);
  }
  
  const user = usersStore.get(id);
  if (user) {
    user.last_login = new Date().toISOString();
    usersStore.set(id, user);
  }
}

export async function deleteUser(id: string): Promise<boolean> {
  const backend = process.env.NEXT_PUBLIC_STORAGE || "memory";
  
  if (backend === "dynamodb") {
    const mod = await import("@/lib/users.dynamo");
    return mod.deleteUser(id);
  }
  
  const user = usersStore.get(id);
  if (!user) return false;
  
  usersStore.delete(id);
  emailToIdStore.delete(user.email);
  
  return true;
}

export async function getUserByStripeCustomerId(stripeCustomerId: string): Promise<User | null> {
  const backend = process.env.NEXT_PUBLIC_STORAGE || "memory";

  if (backend === "dynamodb") {
    const mod = await import("@/lib/users.dynamo");
    return mod.getUserByStripeCustomerId(stripeCustomerId);
  }

  // Memory storage - search through all users
  for (const user of usersStore.values()) {
    if (user.stripe_customer_id === stripeCustomerId) {
      return user;
    }
  }

  return null;
}

export async function listUsers(limit = 50): Promise<User[]> {
  const backend = process.env.NEXT_PUBLIC_STORAGE || "memory";

  if (backend === "dynamodb") {
    const mod = await import("@/lib/users.dynamo");
    return mod.listUsers(limit);
  }

  const users = Array.from(usersStore.values()) as User[];
  users.sort((a, b) => (a.created_at < b.created_at ? 1 : -1));
  return users.slice(0, limit);
}
