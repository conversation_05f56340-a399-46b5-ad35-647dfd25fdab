import { listVideos } from "@/lib/videosStore";
import { getChapters } from "@/lib/chaptersStore";

export default async function VideosPage({ searchParams }: { searchParams: Promise<Record<string, string>> }) {
  const sp = await searchParams;
  const limit = Number(sp.limit || 25);
  const customerId = sp.customer_id || "";

  const videos = await listVideos(customerId || undefined, limit);

  async function renderChapters(videoId: string) {
    "use server";
    const chapters = await getChapters(videoId);
    if (!chapters.length) return <div className="text-xs text-gray-500">No chapters persisted yet.</div>;
    return (
      <ol className="list-decimal list-inside text-sm">
        {chapters.map((c, i) => (
          <li key={`${videoId}-${i}`}>
            <span className="font-mono">{c.start}</span> {" → "}
            <span className="font-mono">{c.end}</span> — {c.title}
          </li>
        ))}
      </ol>
    );
  }

  const params = new URLSearchParams();
  params.set("limit", String(limit));
  if (customerId) params.set("customer_id", customerId);

  return (
    <div className="max-w-5xl mx-auto p-6">
      <h1 className="text-2xl font-semibold mb-4">Recent Videos</h1>
      <form className="flex items-end gap-3 mb-4" action="/videos" method="get">
        <label className="text-sm">
          <div className="mb-1">Customer ID</div>
          <input name="customer_id" defaultValue={customerId} className="border rounded px-2 py-1 w-80" />
        </label>
        <label className="text-sm">
          <div className="mb-1">Limit</div>
          <input name="limit" defaultValue={String(limit)} className="border rounded px-2 py-1 w-20" />
        </label>
        <button className="px-3 py-2 rounded border text-sm" type="submit">Apply</button>
        {(customerId) ? (
          <a className="px-3 py-2 rounded border text-sm" href="/videos">Reset</a>
        ) : null}
      </form>

      {videos.length === 0 ? (
        <p className="text-sm text-gray-600">No videos found.</p>
      ) : (
        <div className="space-y-3">
          {await Promise.all(videos.map(async (v) => (
            <div key={v.video_id} className="border rounded p-3">
              <div className="text-sm text-gray-700 flex flex-wrap gap-4">
                <div>Video: <span className="font-mono">{v.video_id}</span></div>
                <div>Duration: {Math.ceil(v.duration_sec / 60)} min</div>
                <div>Created: {new Date(v.created_at).toLocaleString()}</div>
              </div>
              <details className="mt-2">
                <summary className="cursor-pointer text-sm">Chapters</summary>
                <div className="mt-2">
                  {await renderChapters(v.video_id)}
                </div>
              </details>
            </div>
          )))}
        </div>
      )}
    </div>
  );
}


