"use client";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function SignUpPage() {
  const [email, setEmail] = useState("");
  const [plan, setPlan] = useState<"starter" | "creator" | "pro">("starter");

  function submit(e: React.FormEvent) {
    e.preventDefault();
    try {
      localStorage.setItem("email", email);
      localStorage.setItem("customerId", `cus_${Math.random().toString(36).slice(2, 10)}`);
      localStorage.setItem("plan", plan);
      window.location.href = "/account";
    } catch {}
  }

  return (
    <div className="min-h-screen grid place-items-center px-4 py-12">
      <Card className="w-full max-w-sm p-6">
        <h1 className="text-xl font-semibold mb-4">Create your account</h1>
        <form onSubmit={submit} className="grid gap-3">
          <label className="grid gap-1 text-sm">
            <span>Email</span>
            <Input type="email" value={email} onChange={(e) => setEmail(e.target.value)} required />
          </label>
          <label className="grid gap-1 text-sm">
            <span>Plan</span>
            <select className="border rounded h-9 px-2" value={plan} onChange={(e) => setPlan(e.target.value as any)}>
              <option value="starter">Starter (120 min)</option>
              <option value="creator">Creator (400 min)</option>
              <option value="pro">Pro (1600 min)</option>
            </select>
          </label>
          <Button type="submit" className="mt-2">Create account</Button>
        </form>
        <p className="mt-3 text-xs text-gray-500">Demo signup — no email sent.</p>
      </Card>
    </div>
  );
}
