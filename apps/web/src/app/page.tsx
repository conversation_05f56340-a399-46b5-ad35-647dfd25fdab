import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function Landing() {
  return (
    <div className="min-h-screen">
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(14,165,233,0.10),transparent_60%),radial-gradient(ellipse_at_bottom,rgba(99,102,241,0.10),transparent_60%)]" />
        <div className="relative max-w-6xl mx-auto px-4 py-20 lg:py-28">
          <h1 className="text-4xl lg:text-5xl font-semibold tracking-tight">
            Turn any YouTube video into clean, copy‑ready chapters
          </h1>
          <p className="mt-4 text-base lg:text-lg text-gray-600 dark:text-gray-300 max-w-2xl">
            Paste a URL. Get timestamped sections and better titles in seconds. Export as YouTube
            description, JSON, or TXT.
          </p>
          <div className="mt-8 flex gap-3">
            <Button asChild>
              <Link href="/app">Get started</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/pricing">See pricing</Link>
            </Button>
          </div>
          <p className="mt-3 text-xs text-gray-500">No signup needed for demo. Starter plan: 120 min/month.</p>
        </div>
      </section>

      <section className="max-w-6xl mx-auto px-4 py-12 grid gap-6 lg:grid-cols-3">
        <Card className="p-5">
          <h3 className="font-medium mb-1">Fast & predictable</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300">Deterministic segmentation with transcript‑aware refinement when available.</p>
        </Card>
        <Card className="p-5">
          <h3 className="font-medium mb-1">Export anywhere</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300">Copy as YouTube description or download as TXT/JSON for your workflow.</p>
        </Card>
        <Card className="p-5">
          <h3 className="font-medium mb-1">Fair billing</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300">Only minutes processed count. Plans for 120/400/1600 minutes.</p>
        </Card>
      </section>

      <section className="max-w-6xl mx-auto px-4 pb-16">
        <Card className="p-6">
          <h3 className="font-medium">How it works</h3>
          <ol className="mt-2 list-decimal list-inside text-sm space-y-1">
            <li>Paste a YouTube link on the <Link className="underline" href="/app">generator</Link>.</li>
            <li>We fetch duration, transcribe (if enabled), then compute segments.</li>
            <li>Titles are refined for readability; summaries optional.</li>
            <li>Copy to clipboard or download results.</li>
          </ol>
        </Card>
      </section>
    </div>
  );
}
