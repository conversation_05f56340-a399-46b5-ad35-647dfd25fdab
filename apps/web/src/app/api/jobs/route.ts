import type { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  const storage: any = await import("@/lib/storage");
  const url = new URL(req.url);
  const limit = Number(url.searchParams.get("limit") || 25);
  const customerId = url.searchParams.get("customer_id") || undefined;
  const nextToken = url.searchParams.get("next") || undefined;
  if (typeof storage.listJobs === "function") {
    const result = await storage.listJobs({ limit, customerId, nextToken });
    const body = Array.isArray(result) ? { jobs: result } : result;
    return Response.json(body, { status: 200, headers: { "Cache-Control": "no-store" } });
  }
  return Response.json({ error: "Listing not implemented for current storage backend" }, { status: 501 });
}


