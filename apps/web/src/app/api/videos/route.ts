import type { NextRequest } from "next/server";
import { listVideos } from "@/lib/videosStore";

export async function GET(req: NextRequest) {
  const url = new URL(req.url);
  const limit = Number(url.searchParams.get("limit") || 25);
  const customerId = url.searchParams.get("customer_id") || undefined;
  const items = await listVideos(customerId, limit);
  return Response.json({ videos: items }, { status: 200, headers: { "Cache-Control": "no-store" } });
}


