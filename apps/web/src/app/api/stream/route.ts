import { registerClient } from "@/lib/realtime";
import { parseAuth } from "@/lib/auth";

export async function GET(req: Request) {
  const auth = await parseAuth(req.headers);
  const encoder = new TextEncoder();

  const stream = new ReadableStream<Uint8Array>({
    start(controller) {
      const push = (chunk: string) => controller.enqueue(encoder.encode(chunk));
      const cleanup = registerClient(auth.customer_id, push, () => controller.close());
      const initial = `retry: 2000\n` + `event: ready\n` + `data: {"ok":true}\n\n`;
      controller.enqueue(encoder.encode(initial));
      const keepAlive = setInterval(() => controller.enqueue(encoder.encode(`: keep-alive\n\n`)), 15000);
      (controller as any)._cleanup = () => {
        clearInterval(keepAlive);
        cleanup();
      };
    },
    cancel() {
      const anyCtrl: any = this as any;
      if (anyCtrl?._cleanup) anyCtrl._cleanup();
    },
  });

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache, no-transform",
      Connection: "keep-alive",
    },
  });
}


