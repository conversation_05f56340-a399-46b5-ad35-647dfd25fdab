import * as memory from "@/lib/store";

export async function POST(req: Request) {
  const { action, jobId, data } = await req.json();
  
  if (action === "save") {
    memory.saveJob(data);
    return Response.json({ success: true, message: "Job saved" });
  }
  
  if (action === "get") {
    const job = memory.getJob(jobId);
    return Response.json({ job, found: !!job });
  }
  
  return Response.json({ error: "Invalid action" }, { status: 400 });
}
