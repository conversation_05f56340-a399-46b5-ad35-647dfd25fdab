import { NextRequest } from "next/server";
import { parseAuth } from "@/lib/auth";
import { getUserById } from "@/lib/users";

export const runtime = "nodejs";

type CreateCheckoutRequestBody = {
  plan: "starter" | "creator" | "pro";
};

const PLAN_PRICE_IDS = {
  starter: process.env.STRIPE_STARTER_PRICE_ID || "price_starter",
  creator: process.env.STRIPE_CREATOR_PRICE_ID || "price_creator", 
  pro: process.env.STRIPE_PRO_PRICE_ID || "price_pro",
};

export async function POST(req: NextRequest) {
  try {
    const auth = await parseAuth(req.headers);
    
    if (!auth.is_authenticated) {
      return Response.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = (await req.json()) as CreateCheckoutRequestBody;
    
    if (!body?.plan || !["starter", "creator", "pro"].includes(body.plan)) {
      return Response.json(
        { error: "Invalid plan specified" },
        { status: 400 }
      );
    }

    // Get user details
    const user = await getUserById(auth.user_id);
    if (!user) {
      return Response.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if user is already on this plan
    if (user.plan === body.plan) {
      return Response.json(
        { error: "User is already on this plan" },
        { status: 400 }
      );
    }

    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    if (!stripeSecretKey) {
      return Response.json(
        { error: "Billing not configured" },
        { status: 503 }
      );
    }

    const { default: Stripe } = await import('stripe');
    const stripe = new Stripe(stripeSecretKey);
    
    // Create or get Stripe customer
    let stripeCustomerId = user.stripe_customer_id;
    
    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        metadata: {
          user_id: user.id,
          customer_id: user.customer_id,
        },
      });
      stripeCustomerId = customer.id;
      
      // Update user with Stripe customer ID
      const { updateUser } = await import("@/lib/users");
      await updateUser(user.id, { stripe_customer_id: stripeCustomerId });
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: stripeCustomerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: PLAN_PRICE_IDS[body.plan],
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${req.headers.get('origin')}/account?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${req.headers.get('origin')}/pricing`,
      metadata: {
        user_id: user.id,
        plan: body.plan,
      },
    });

    return Response.json({
      checkout_url: session.url,
      session_id: session.id,
    }, { status: 200 });

  } catch (error) {
    console.error('Create checkout error:', error);
    return Response.json(
      { error: "Failed to create checkout session" },
      { status: 500 }
    );
  }
}
