import { NextRequest } from "next/server";

export const runtime = "nodejs";

export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const signature = req.headers.get('stripe-signature');
    
    if (!signature) {
      return Response.json(
        { error: "Missing stripe-signature header" },
        { status: 400 }
      );
    }

    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    
    if (!stripeSecretKey || !webhookSecret) {
      console.error('Missing Stripe configuration');
      return Response.json(
        { error: "Webhook not configured" },
        { status: 503 }
      );
    }

    const { default: Stripe } = await import('stripe');
    const stripe = new Stripe(stripeSecretKey);
    
    let event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message);
      return Response.json(
        { error: "Invalid signature" },
        { status: 400 }
      );
    }

    console.log('Received Stripe webhook:', event.type);

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object);
        break;
        
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
        
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
        
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;
        
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;
        
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return Response.json({ received: true }, { status: 200 });

  } catch (error) {
    console.error('Webhook error:', error);
    return Response.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    );
  }
}

async function handleCheckoutCompleted(session: any) {
  try {
    const { user_id, plan } = session.metadata;
    
    if (!user_id || !plan) {
      console.error('Missing metadata in checkout session:', session.id);
      return;
    }

    // Update user's plan
    const { updateUser } = await import("@/lib/users");
    await updateUser(user_id, { 
      plan: plan as "starter" | "creator" | "pro",
      stripe_customer_id: session.customer,
    });

    console.log(`Updated user ${user_id} to plan ${plan}`);

  } catch (error) {
    console.error('Error handling checkout completed:', error);
  }
}

async function handleSubscriptionUpdated(subscription: any) {
  try {
    const customerId = subscription.customer;
    
    // Find user by Stripe customer ID
    const { getUserByStripeCustomerId } = await import("@/lib/users");
    const user = await getUserByStripeCustomerId?.(customerId);
    
    if (!user) {
      console.error('User not found for Stripe customer:', customerId);
      return;
    }

    // Determine plan from subscription
    const plan = determinePlanFromSubscription(subscription);
    
    if (plan && user.plan !== plan) {
      const { updateUser } = await import("@/lib/users");
      await updateUser(user.id, { plan });
      console.log(`Updated user ${user.id} plan to ${plan}`);
    }

  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

async function handleSubscriptionDeleted(subscription: any) {
  try {
    const customerId = subscription.customer;
    
    // Find user by Stripe customer ID
    const { getUserByStripeCustomerId } = await import("@/lib/users");
    const user = await getUserByStripeCustomerId?.(customerId);
    
    if (!user) {
      console.error('User not found for Stripe customer:', customerId);
      return;
    }

    // Downgrade to starter plan
    const { updateUser } = await import("@/lib/users");
    await updateUser(user.id, { plan: "starter" });
    
    console.log(`Downgraded user ${user.id} to starter plan`);

  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}

async function handlePaymentSucceeded(invoice: any) {
  try {
    console.log(`Payment succeeded for invoice ${invoice.id}`);
    // Could send confirmation email here
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

async function handlePaymentFailed(invoice: any) {
  try {
    console.log(`Payment failed for invoice ${invoice.id}`);
    // Could send payment failure notification here
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

function determinePlanFromSubscription(subscription: any): "starter" | "creator" | "pro" | null {
  const priceIds = {
    [process.env.STRIPE_STARTER_PRICE_ID || "price_starter"]: "starter",
    [process.env.STRIPE_CREATOR_PRICE_ID || "price_creator"]: "creator", 
    [process.env.STRIPE_PRO_PRICE_ID || "price_pro"]: "pro",
  };

  for (const item of subscription.items.data) {
    const plan = priceIds[item.price.id];
    if (plan) {
      return plan as "starter" | "creator" | "pro";
    }
  }

  return null;
}
