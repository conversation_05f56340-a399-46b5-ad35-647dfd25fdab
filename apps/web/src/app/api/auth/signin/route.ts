import { NextRequest } from "next/server";
import { signIn } from "@/lib/auth";
import { rateLimit, getClientIP, RATE_LIMITS, createRateLimitResponse } from "@/lib/rateLimit";

export const runtime = "nodejs";

type SignInRequestBody = {
  email: string;
  password: string;
};

export async function POST(req: NextRequest) {
  try {
    // Rate limiting
    const clientIP = getClientIP(req.headers);
    const rateLimitResult = rateLimit(`auth:signin:${clientIP}`, RATE_LIMITS.AUTH_API);

    if (!rateLimitResult.allowed) {
      return createRateLimitResponse(rateLimitResult.resetTime);
    }

    const body = (await req.json()) as SignInRequestBody;
    
    // Validate input
    if (!body?.email || !body?.password) {
      return Response.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }
    
    // Attempt signin
    const result = await signIn(body.email, body.password);
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 401 }
      );
    }
    
    // Return success with token
    return Response.json({
      success: true,
      token: result.token,
      user: {
        id: result.user!.id,
        email: result.user!.email,
        customer_id: result.user!.customer_id,
        plan: result.user!.plan,
        created_at: result.user!.created_at,
        last_login: result.user!.last_login,
        email_verified: result.user!.email_verified,
      },
    }, { status: 200 });
    
  } catch (error) {
    console.error('Signin API error:', error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
