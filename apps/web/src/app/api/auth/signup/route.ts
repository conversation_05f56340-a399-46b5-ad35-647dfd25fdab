import { NextRequest } from "next/server";
import { signUp } from "@/lib/auth";
import { rateLimit, getClientIP, RATE_LIMITS, createRateLimitResponse } from "@/lib/rateLimit";

export const runtime = "nodejs";

type SignUpRequestBody = {
  email: string;
  password: string;
  confirmPassword?: string;
};

export async function POST(req: NextRequest) {
  try {
    // Rate limiting
    const clientIP = getClientIP(req.headers);
    const rateLimitResult = rateLimit(`auth:signup:${clientIP}`, RATE_LIMITS.AUTH_API);

    if (!rateLimitResult.allowed) {
      return createRateLimitResponse(rateLimitResult.resetTime);
    }

    const body = (await req.json()) as SignUpRequestBody;
    
    // Validate input
    if (!body?.email || !body?.password) {
      return Response.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }
    
    if (body.confirmPassword && body.password !== body.confirmPassword) {
      return Response.json(
        { error: "Passwords do not match" },
        { status: 400 }
      );
    }
    
    // Attempt signup
    const result = await signUp(body.email, body.password);
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      );
    }
    
    // Return success (don't include sensitive data)
    return Response.json({
      success: true,
      user: {
        id: result.user!.id,
        email: result.user!.email,
        customer_id: result.user!.customer_id,
        plan: result.user!.plan,
        created_at: result.user!.created_at,
        email_verified: result.user!.email_verified,
      },
    }, { status: 201 });
    
  } catch (error) {
    console.error('Signup API error:', error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
