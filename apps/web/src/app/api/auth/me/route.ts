import { NextRequest } from "next/server";
import { parseAuth } from "@/lib/auth";
import { getUserById } from "@/lib/users";

export const runtime = "nodejs";

export async function GET(req: NextRequest) {
  try {
    const auth = await parseAuth(req.headers);
    
    if (!auth.is_authenticated) {
      return Response.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get fresh user data
    const user = await getUserById(auth.user_id);
    if (!user) {
      return Response.json(
        { error: "User not found" },
        { status: 404 }
      );
    }
    
    // Return user info (without sensitive data)
    return Response.json({
      user: {
        id: user.id,
        email: user.email,
        customer_id: user.customer_id,
        plan: user.plan,
        created_at: user.created_at,
        last_login: user.last_login,
        email_verified: user.email_verified,
        stripe_customer_id: user.stripe_customer_id,
      },
    }, { status: 200 });
    
  } catch (error) {
    console.error('Get user API error:', error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
