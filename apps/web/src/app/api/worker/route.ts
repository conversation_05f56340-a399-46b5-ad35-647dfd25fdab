import { setEventHandler, drainQueue } from "@/lib/bus";
import { updateJobStatus, completeJob } from "@/lib/storage";

setEventHandler(async (event) => {
  if (event.type === "VideoSubmitted") {
    const { jobId, estimatedMinutes } = event.payload;
    updateJobStatus(jobId, "processing");
    // Simulate processing fan-out and completion
    await new Promise((r) => setTimeout(r, 1000));
    const demoChapters = [
      { start: "00:00:00", end: "00:05:00", duration_sec: 300, title: "Intro" },
      { start: "00:05:00", end: "00:12:00", duration_sec: 420, title: "Concepts" },
      { start: "00:12:00", end: "00:20:00", duration_sec: 480, title: "Wrap-up" },
    ];
    completeJob(jobId, demoChapters, estimatedMinutes);
  }
});

export async function POST() {
  await drainQueue();
  return Response.json({ drained: true }, { status: 200 });
}


