"use client";
import { useEffect, useState } from "react";
import Link from "next/link";

type Usage = {
  customer_id: string;
  plan: "starter" | "creator" | "pro";
  used: number;
  allowance: number;
  remaining: number;
};

export default function AccountPage() {
  const [usage, setUsage] = useState<Usage | null>(null);
  const [records, setRecords] = useState<Array<{ minutes: number; timestamp: number; job_id: string }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      try {
        const token = localStorage.getItem("authToken");
        if (!token) {
          setError("Please sign in to view your account");
          setLoading(false);
          return;
        }

        const res = await fetch("/api/usage", {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (!res.ok) {
          if (res.status === 401) {
            setError("Session expired. Please sign in again.");
            localStorage.removeItem("authToken");
            localStorage.removeItem("user");
          } else {
            setError("Failed to load usage data");
          }
          setLoading(false);
          return;
        }

        const data = await res.json();
        setUsage({ customer_id: data.customer_id, plan: data.plan, used: data.used, allowance: data.allowance, remaining: data.remaining });
        setRecords(Array.isArray(data.records) ? data.records : []);
      } catch (e: any) {
        setError(e.message || "Failed to load usage");
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">Account</h1>

      {loading && <p>Loading…</p>}
      {error && <p className="text-red-600">{error}</p>}

      {usage && (
        <div className="grid gap-6 md:grid-cols-2">
          {/* Subscription Card */}
          <div className="bg-white border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Subscription</h2>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Current Plan</span>
                <span className="font-medium capitalize text-lg">{usage.plan}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Monthly Allowance</span>
                <span className="font-medium">{usage.allowance} minutes</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Used This Month</span>
                <span className="font-medium">{usage.used} minutes</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Remaining</span>
                <span className={`font-medium ${usage.remaining <= 10 ? 'text-orange-600' : 'text-green-600'}`}>
                  {usage.remaining} minutes
                </span>
              </div>

              <div className="pt-4 border-t">
                <div className="flex gap-3">
                  <Link
                    href="/pricing"
                    className="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded hover:bg-blue-700 transition-colors"
                  >
                    {usage.plan === 'starter' ? 'Upgrade Plan' : 'Change Plan'}
                  </Link>
                  <button className="px-4 py-2 border rounded hover:bg-gray-50 transition-colors">
                    Billing
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Usage Overview Card */}
          <div className="bg-white border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Usage Overview</h2>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Monthly Usage</span>
                  <span>{Math.round((usage.used / usage.allowance) * 100)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      usage.used / usage.allowance > 0.9 ? 'bg-red-500' :
                      usage.used / usage.allowance > 0.7 ? 'bg-orange-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${Math.min((usage.used / usage.allowance) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>

              <div className="text-sm text-gray-600 space-y-1">
                <div>Customer ID: <span className="font-mono text-xs">{usage.customer_id}</span></div>
                <div>Billing Cycle: Monthly</div>
                <div>Overage Rate: $0.05/minute</div>
              </div>

              {usage.remaining <= 10 && (
                <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded">
                  <div className="text-sm text-orange-800">
                    <strong>Low on minutes!</strong> Consider upgrading your plan to avoid overage charges.
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Usage Records */}
      {usage && (
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Usage History</h2>
          <div className="bg-white border rounded-lg p-6">
            {records.length === 0 ? (
              <div className="text-gray-500 text-center py-8">No usage records yet.</div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left p-3 border-b">Date</th>
                      <th className="text-left p-3 border-b">Minutes Used</th>
                      <th className="text-left p-3 border-b">Job ID</th>
                    </tr>
                  </thead>
                  <tbody>
                    {records.map((r, i) => (
                      <tr key={`${r.job_id}-${i}`} className="border-b hover:bg-gray-50">
                        <td className="p-3">{new Date(r.timestamp).toLocaleDateString()}</td>
                        <td className="p-3 font-medium">{r.minutes}</td>
                        <td className="p-3 font-mono text-xs text-gray-600">{r.job_id}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}


