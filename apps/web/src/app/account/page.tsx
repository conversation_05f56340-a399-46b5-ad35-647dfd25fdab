"use client";
import { useEffect, useState } from "react";

type Usage = {
  customer_id: string;
  plan: "starter" | "creator" | "pro";
  used: number;
  allowance: number;
  remaining: number;
};

export default function AccountPage() {
  const [usage, setUsage] = useState<Usage | null>(null);
  const [records, setRecords] = useState<Array<{ minutes: number; timestamp: number; job_id: string }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      try {
        const token = localStorage.getItem("authToken");
        if (!token) {
          setError("Please sign in to view your account");
          setLoading(false);
          return;
        }

        const res = await fetch("/api/usage", {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (!res.ok) {
          if (res.status === 401) {
            setError("Session expired. Please sign in again.");
            localStorage.removeItem("authToken");
            localStorage.removeItem("user");
          } else {
            setError("Failed to load usage data");
          }
          setLoading(false);
          return;
        }

        const data = await res.json();
        setUsage({ customer_id: data.customer_id, plan: data.plan, used: data.used, allowance: data.allowance, remaining: data.remaining });
        setRecords(Array.isArray(data.records) ? data.records : []);
      } catch (e: any) {
        setError(e.message || "Failed to load usage");
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  return (
    <div className="max-w-3xl mx-auto p-6">
      <h1 className="text-2xl font-semibold mb-4">Account</h1>
      {loading && <p>Loading…</p>}
      {error && <p className="text-red-600">{error}</p>}
      {usage && (
        <div className="space-y-2 text-sm">
          <div>Customer: <span className="font-mono">{usage.customer_id}</span></div>
          <div>Plan: {usage.plan}</div>
          <div>Usage: {usage.used}/{usage.allowance} min ({usage.remaining} remaining)</div>
          <div className="mt-4">
            <h2 className="font-medium mb-2">Usage records</h2>
            {records.length === 0 ? (
              <div className="text-gray-500">No records yet.</div>
            ) : (
              <div className="overflow-x-auto border rounded">
                <table className="w-full text-xs">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left p-2 border-r">When</th>
                      <th className="text-left p-2 border-r">Minutes</th>
                      <th className="text-left p-2">Job</th>
                    </tr>
                  </thead>
                  <tbody>
                    {records.map((r, i) => (
                      <tr key={`${r.job_id}-${i}`} className="border-t">
                        <td className="p-2 border-r">{new Date(r.timestamp).toLocaleString()}</td>
                        <td className="p-2 border-r">{r.minutes}</td>
                        <td className="p-2 font-mono">{r.job_id}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}


