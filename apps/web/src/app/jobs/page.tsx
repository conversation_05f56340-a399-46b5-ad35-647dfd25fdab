import Link from "next/link";

export default async function JobsPage({ searchParams }: { searchParams: Promise<Record<string, string>> }) {
  const sp = await searchParams;
  const limit = Number(sp.limit || 25);
  const customerId = sp.customer_id || "";
  const next = sp.next || "";

  const params = new URLSearchParams();
  params.set("limit", String(limit));
  if (customerId) params.set("customer_id", customerId);
  if (next) params.set("next", next);

  const storage: any = await import("@/lib/storage");
  const result = typeof storage.listJobs === "function" ? await storage.listJobs({ limit, customerId: customerId || undefined, nextToken: next || undefined }) : { jobs: [] };
  const jobs = Array.isArray(result) ? result : (result.jobs || []);
  const nextToken = Array.isArray(result) ? undefined : result.nextToken;

  const buildUrl = (overrides: Record<string, string | undefined>) => {
    const q = new URLSearchParams(params);
    Object.entries(overrides).forEach(([k, v]) => {
      if (!v) q.delete(k);
      else q.set(k, v);
    });
    return `/jobs?${q.toString()}`;
  };

  return (
    <div className="max-w-5xl mx-auto p-6">
      <h1 className="text-2xl font-semibold mb-4">Recent Jobs</h1>
      <form className="flex items-end gap-3 mb-4" action="/jobs" method="get">
        <label className="text-sm">
          <div className="mb-1">Customer ID</div>
          <input name="customer_id" defaultValue={customerId} className="border rounded px-2 py-1 w-80" />
        </label>
        <label className="text-sm">
          <div className="mb-1">Limit</div>
          <input name="limit" defaultValue={String(limit)} className="border rounded px-2 py-1 w-20" />
        </label>
        <button className="px-3 py-2 rounded border text-sm" type="submit">Apply</button>
        {customerId || next ? (
          <Link className="px-3 py-2 rounded border text-sm" href="/jobs">Reset</Link>
        ) : null}
      </form>
      {jobs.length === 0 ? (
        <p className="text-sm text-gray-600">No jobs found.</p>
      ) : (
        <div className="overflow-x-auto border rounded">
          <table className="w-full text-sm">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left p-2 border-r">Job ID</th>
                <th className="text-left p-2 border-r">Video ID</th>
                <th className="text-left p-2 border-r">Status</th>
                <th className="text-left p-2">Created</th>
              </tr>
            </thead>
            <tbody>
              {jobs.map((j: any) => (
                <tr key={j.job_id} className="border-t">
                  <td className="p-2 border-r">
                    <Link className="text-blue-600 hover:underline" href={`/jobs/${j.job_id}`}>
                      {j.job_id}
                    </Link>
                  </td>
                  <td className="p-2 border-r">{j.video_id}</td>
                  <td className="p-2 border-r">{j.status}</td>
                  <td className="p-2">{j.created_at}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      <div className="mt-4 flex gap-3">
        {nextToken && (
          <Link className="px-3 py-2 rounded border text-sm" href={buildUrl({ next: nextToken })}>
            Next →
          </Link>
        )}
      </div>
    </div>
  );
}


