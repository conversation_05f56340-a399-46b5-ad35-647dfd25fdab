import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const plans = [
  {
    name: "Starter",
    price: "$0",
    period: "/mo",
    features: ["120 minutes / month", "Basic titles", "Email support"],
    cta: "Get started",
    highlight: false,
  },
  {
    name: "Creator",
    price: "$19",
    period: "/mo",
    features: ["400 minutes / month", "Improved titles", "Priority support"],
    cta: "Upgrade",
    highlight: true,
  },
  {
    name: "Pro",
    price: "$59",
    period: "/mo",
    features: ["1,600 minutes / month", "Best titles & summaries", "SLA support"],
    cta: "Contact sales",
    highlight: false,
  },
];

export default function PricingPage() {
  return (
    <div className="min-h-screen">
      <section className="max-w-6xl mx-auto px-4 py-16">
        <div className="text-center mb-10">
          <h1 className="text-4xl font-semibold tracking-tight">Simple pricing</h1>
          <p className="mt-2 text-gray-600">Only minutes processed count. Scale up as you grow.</p>
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          {plans.map((p) => (
            <Card key={p.name} className={p.highlight ? "border-black" : undefined}>
              <div className="p-6 flex flex-col h-full">
                <div className="mb-4">
                  <h3 className="text-xl font-medium">{p.name}</h3>
                  <div className="mt-2 text-3xl font-semibold">
                    {p.price} <span className="text-base font-normal text-gray-500">{p.period}</span>
                  </div>
                </div>
                <ul className="text-sm space-y-2 flex-1">
                  {p.features.map((f) => (
                    <li key={f} className="text-gray-700">• {f}</li>
                  ))}
                </ul>
                <div className="mt-6">
                  <Button className="w-full" variant={p.highlight ? "default" : "outline"}>{p.cta}</Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
        <p className="mt-6 text-xs text-gray-500 text-center">Overage: $0.05/min. Cancel anytime.</p>
      </section>
    </div>
  );
}
