"use client";
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";

type User = {
  id: string;
  email: string;
  plan: "starter" | "creator" | "pro";
  customer_id: string;
};

const plans = [
  {
    name: "Starter",
    price: "$9",
    period: "/mo",
    features: ["120 minutes / month", "Basic chapter generation", "Copy/download chapters", "Email support"],
    cta: "Get started",
    highlight: false,
    planId: "starter",
  },
  {
    name: "Creator",
    price: "$29",
    period: "/mo",
    features: ["400 minutes / month", "Enhanced AI titles", "Priority processing", "Bulk operations", "Priority support"],
    cta: "Upgrade",
    highlight: true,
    planId: "creator",
  },
  {
    name: "Pro",
    price: "$99",
    period: "/mo",
    features: ["1,600 minutes / month", "API access", "Custom integrations", "White-label options", "Dedicated support"],
    cta: "Contact sales",
    highlight: false,
    planId: "pro",
  },
];

export default function PricingPage() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuth();
  }, []);

  async function checkAuth() {
    try {
      const token = localStorage.getItem("authToken");
      if (!token) {
        setLoading(false);
        return;
      }

      const res = await fetch("/api/auth/me", {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (res.ok) {
        const data = await res.json();
        setUser(data.user);
      }
    } catch (error) {
      console.error("Auth check failed:", error);
    } finally {
      setLoading(false);
    }
  }

  async function handleUpgrade(planId: string) {
    if (!user) {
      // Redirect to signup
      window.location.href = "/signup";
      return;
    }

    try {
      const token = localStorage.getItem("authToken");
      const res = await fetch("/api/billing/create-checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ plan: planId }),
      });

      if (res.ok) {
        const data = await res.json();
        window.location.href = data.checkout_url;
      } else {
        alert("Failed to create checkout session. Please try again.");
      }
    } catch (error) {
      console.error("Checkout error:", error);
      alert("Failed to create checkout session. Please try again.");
    }
  }

  return (
    <div className="min-h-screen">
      <section className="max-w-6xl mx-auto px-4 py-16">
        <div className="text-center mb-10">
          <h1 className="text-4xl font-semibold tracking-tight">Choose Your Plan</h1>
          <p className="mt-2 text-gray-600">Transform your YouTube videos with AI-powered chapter generation</p>
          {user && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg inline-block">
              <p className="text-sm text-blue-700">
                Current plan: <strong>{user.plan}</strong>
              </p>
            </div>
          )}
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          {plans.map((p) => (
            <Card key={p.name} className={p.highlight ? "border-blue-500 border-2 shadow-lg" : undefined}>
              <div className="p-6 flex flex-col h-full">
                {p.highlight && (
                  <div className="text-center mb-4">
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <div className="mb-4">
                  <h3 className="text-xl font-medium">{p.name}</h3>
                  <div className="mt-2 text-3xl font-semibold">
                    {p.price} <span className="text-base font-normal text-gray-500">{p.period}</span>
                  </div>
                </div>
                <ul className="text-sm space-y-2 flex-1">
                  {p.features.map((f) => (
                    <li key={f} className="flex items-center text-gray-700">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {f}
                    </li>
                  ))}
                </ul>
                <div className="mt-6">
                  <Button
                    className="w-full"
                    variant={p.highlight ? "default" : "outline"}
                    onClick={() => handleUpgrade(p.planId)}
                    disabled={loading || (user?.plan === p.planId)}
                  >
                    {loading
                      ? "Loading..."
                      : user?.plan === p.planId
                      ? "Current Plan"
                      : user
                      ? "Upgrade"
                      : p.cta}
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center space-y-4">
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-2">Additional Usage</h3>
            <p className="text-gray-600">
              Need more minutes? Additional usage is billed at{" "}
              <strong>$0.05 per minute</strong> beyond your plan&apos;s allowance.
            </p>
          </div>

          <div className="text-sm text-gray-500 space-y-1">
            <p>• All plans include unlimited videos up to 120 minutes each</p>
            <p>• Cancel anytime, no long-term commitments</p>
            <p>• 14-day money-back guarantee</p>
          </div>

          {!user && (
            <div className="mt-8">
              <p className="text-gray-600 mb-4">
                Already have an account?{" "}
                <Link href="/signin" className="text-blue-600 hover:underline">
                  Sign in
                </Link>
              </p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
