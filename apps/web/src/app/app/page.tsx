"use client";
import { use<PERSON>allback, useMemo, useState } from "react";
import { formatChaptersForYouTube } from "@/lib/format";
import { extractYouTubeVideoId } from "@/lib/url";
import { UploadButton } from "@uploadthing/react";
import type { OurFileRouter } from "../api/uploadthing/core";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";

type ApiJob = {
  job_id: string;
  video_id: string;
  status: string;
  estimated_minutes: number;
  billing_preview_minutes: number;
  minutes_billed?: number;
  chapters?: { start: string; end: string; duration_sec: number; title: string; summary?: string }[];
  customer_id?: string;
};

export default function GeneratorApp() {
  const [url, setUrl] = useState("");
  const [job, setJob] = useState<ApiJob | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [usage, setUsage] = useState<{ used: number; allowance: number; remaining: number } | null>(null);
  const [plan, setPlan] = useState<"starter" | "creator" | "pro">("starter");
  const [density, setDensity] = useState<"auto" | "sparse" | "dense">("auto");
  const [titleStyle, setTitleStyle] = useState<"concise" | "descriptive">("concise");
  const [includeSummaries, setIncludeSummaries] = useState(false);

  const videoId = useMemo(() => extractYouTubeVideoId(url) || "", [url]);

  const refreshUsage = useCallback(async () => {
    try {
      const u = await fetch("/api/usage", { headers: { "x-plan": plan } }).then((r) => r.json());
      setUsage({ used: u.used, allowance: u.allowance, remaining: u.remaining });
    } catch {}
  }, [plan]);

  const submit = useCallback(async () => {
    setError(null);
    setLoading(true);
    try {
      await refreshUsage();
      const res = await fetch("/api/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json", "x-plan": plan },
        body: JSON.stringify({ url, options: { chapter_density: density, title_style: titleStyle, include_summaries: includeSummaries } }),
      });
      if (!res.ok) {
        const errJson = await res.json().catch(() => ({}));
        if (res.status === 402) throw new Error(errJson?.message || "Quota exceeded");
        throw new Error(`Request failed (${res.status})`);
      }
      const data: ApiJob = await res.json();
      setJob(data);
      let tries = 0;
      const poll = async () => {
        if (!data.job_id) return;
        const r = await fetch(`/api/jobs/${data.job_id}`);
        if (!r.ok) return;
        const j: ApiJob = await r.json();
        setJob(j);
        if (j.status !== "completed" && tries < 40) {
          tries += 1;
          setTimeout(poll, 500);
        } else if (j.status === "completed") {
          await refreshUsage();
        }
      };
      poll();
    } catch (e: any) {
      setError(e.message || "Failed to submit");
    } finally {
      setLoading(false);
    }
  }, [url, plan, density, titleStyle, includeSummaries, refreshUsage]);

  return (
    <div className="min-h-screen bg-[var(--background)]">
      <section className="border-b bg-gradient-to-b from-white/20 to-transparent dark:from-white/5">
        <div className="max-w-6xl mx-auto px-4 py-10 lg:py-14 grid gap-6 lg:grid-cols-[1.6fr_1fr]">
          <Card className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Input placeholder="https://www.youtube.com/watch?v=VIDEO_ID" value={url} onChange={(e) => setUrl(e.target.value)} />
              <Button onClick={submit} disabled={loading || !videoId}>{loading ? "Submitting…" : "Generate"}</Button>
            </div>
            {!videoId && url && <div className="text-xs text-red-600 mb-2">Enter a valid YouTube video URL.</div>}
            {usage && <div className="text-xs text-gray-600 mb-2">Usage: {usage.used}/{usage.allowance} min · Remaining {usage.remaining}</div>}
            <div className="grid sm:grid-cols-2 gap-3">
              <label className="text-sm grid gap-1">
                <span className="text-gray-600">Plan</span>
                <Select value={plan} onChange={(e) => setPlan(e.target.value as any)}>
                  <option value="starter">Starter (120 min)</option>
                  <option value="creator">Creator (400 min)</option>
                  <option value="pro">Pro (1600 min)</option>
                </Select>
              </label>
              <label className="text-sm grid gap-1">
                <span className="text-gray-600">Density</span>
                <Select value={density} onChange={(e) => setDensity(e.target.value as any)}>
                  <option value="auto">Auto</option>
                  <option value="sparse">Sparse</option>
                  <option value="dense">Dense</option>
                </Select>
              </label>
              <label className="text-sm grid gap-1">
                <span className="text-gray-600">Title style</span>
                <Select value={titleStyle} onChange={(e) => setTitleStyle(e.target.value as any)}>
                  <option value="concise">Concise</option>
                  <option value="descriptive">Descriptive</option>
                </Select>
              </label>
              <label className="text-sm flex items-center gap-2 mt-6">
                <Checkbox checked={includeSummaries} onChange={(e: any) => setIncludeSummaries(e.target.checked)} />
                <span>Include summaries</span>
              </label>
            </div>
            {error && <div className="mt-3 text-red-700 bg-red-50 border border-red-200 rounded p-2 text-sm">{error}</div>}
          </Card>
          <Card className="p-4 min-h-[200px]">
            <h3 className="font-medium mb-2 text-sm">Preview</h3>
            {videoId ? (
              <div className="aspect-video rounded overflow-hidden border bg-black/5">
                <iframe
                  key={videoId}
                  className="w-full h-full"
                  src={`https://www.youtube.com/embed/${videoId}`}
                  title="YouTube video preview"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  allowFullScreen
                />
              </div>
            ) : (
              <p className="text-sm text-gray-500">Paste a URL to see the video preview.</p>
            )}
          </Card>
        </div>
      </section>

      <section className="max-w-6xl mx-auto px-4 py-8">
        {job && (
          <Card className="p-4">
            <div className="text-sm text-gray-700 dark:text-gray-300">
              Job <span className="font-mono">{job.job_id}</span> — Status: {job.status} · Minutes: {job.estimated_minutes}
            </div>
            {job.status === "completed" && job.chapters && (
              <div className="mt-3">
                <div className="flex flex-wrap items-center gap-2 text-xs text-gray-500 mb-2">
                  {job.minutes_billed != null && <span>Billed: {job.minutes_billed} min</span>}
                  {job.customer_id && <span>Customer: {job.customer_id}</span>}
                </div>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  {job.chapters.map((c, idx) => (
                    <li key={idx}>
                      <span className="font-mono">{c.start}</span> {" → "}
                      <span className="font-mono">{c.end}</span> — {c.title}
                      {c.summary && <span className="text-gray-500"> — {c.summary}</span>}
                    </li>
                  ))}
                </ol>
                <div className="mt-3 flex flex-wrap gap-2">
                  <Button onClick={() => navigator.clipboard.writeText(formatChaptersForYouTube(job.chapters!))}>Copy YouTube description</Button>
                  <Button variant="outline" onClick={() => {
                    const text = formatChaptersForYouTube(job.chapters!);
                    const blob = new Blob([text], { type: "text/plain;charset=utf-8" });
                    const u = URL.createObjectURL(blob);
                    const a = document.createElement("a");
                    a.href = u;
                    a.download = `chapters_${job.video_id}.txt`;
                    document.body.appendChild(a);
                    a.click();
                    a.remove();
                    URL.revokeObjectURL(u);
                  }}>Download .txt</Button>
                  <Button variant="outline" onClick={() => navigator.clipboard.writeText(JSON.stringify(job, null, 2))}>Copy JSON</Button>
                </div>
              </div>
            )}
          </Card>
        )}
      </section>
    </div>
  );
}
