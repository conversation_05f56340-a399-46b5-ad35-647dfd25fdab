"use client";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function SignInPage() {
  const [email, setEmail] = useState("");
  const [apiKey, setApiKey] = useState("");

  function submit(e: React.FormEvent) {
    e.preventDefault();
    try {
      if (email) localStorage.setItem("email", email);
      if (apiKey) localStorage.setItem("apiKey", apiKey);
      localStorage.setItem("customerId", "cus_demo");
      window.location.href = "/account";
    } catch {}
  }

  return (
    <div className="min-h-screen grid place-items-center px-4 py-12">
      <Card className="w-full max-w-sm p-6">
        <h1 className="text-xl font-semibold mb-4">Sign in</h1>
        <form onSubmit={submit} className="grid gap-3">
          <label className="grid gap-1 text-sm">
            <span>Email</span>
            <Input type="email" value={email} onChange={(e) => setEmail(e.target.value)} required />
          </label>
          <label className="grid gap-1 text-sm">
            <span>API Key (optional)</span>
            <Input value={apiKey} onChange={(e) => setApiKey(e.target.value)} placeholder="sk_live_..." />
          </label>
          <Button type="submit" className="mt-2">Continue</Button>
        </form>
        <p className="mt-3 text-xs text-gray-500">No real auth yet. This stores demo values locally.</p>
      </Card>
    </div>
  );
}
