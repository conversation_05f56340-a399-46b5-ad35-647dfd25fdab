"use client";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";

export function HeaderAuth() {
  const [authed, setAuthed] = useState(false);
  useEffect(() => {
    try {
      const apiKey = localStorage.getItem("apiKey");
      const customerId = localStorage.getItem("customerId");
      setAuthed(Boolean(apiKey || customerId));
    } catch {}
  }, []);

  if (!authed) {
    return (
      <div className="flex items-center gap-2">
        <Button asChild size="sm" variant="ghost"><Link href="/signin">Sign in</Link></Button>
        <Button asChild size="sm"><Link href="/signup">Sign up</Link></Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Button asChild size="sm" variant="outline"><Link href="/account">Account</Link></Button>
      <Button size="sm" onClick={() => { try { localStorage.removeItem("apiKey"); localStorage.removeItem("customerId"); localStorage.removeItem("email"); } catch {}; window.location.href = "/"; }}>Sign out</Button>
    </div>
  );
}
