openapi: 3.0.3
info:
  title: Bulk YouTube Chapter Generator API
  version: 0.1.0
paths:
  /api/generate:
    post:
      summary: Submit a YouTube URL to generate chapters
      parameters:
        - in: header
          name: Authorization
          required: false
          schema:
            type: string
            example: Bearer sk_test_xxx
        - in: header
          name: Idempotency-Key
          required: false
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                url:
                  type: string
                options:
                  type: object
                  properties:
                    chapter_density:
                      type: string
                      enum: [auto, sparse, dense]
                    language_hint:
                      type: string
                    title_style:
                      type: string
                      enum: [concise, descriptive]
                    include_summaries:
                      type: boolean
              required: [url]
      responses:
        '202':
          description: Accepted; job queued
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobRecord'
        '400':
          description: Invalid request
        '402':
          description: Quota exceeded
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                  code:
                    type: string
                    example: QUOTA_EXCEEDED
                  minutes_needed:
                    type: integer
                  remaining_minutes:
                    type: integer
                  plan:
                    type: string
                    enum: [starter, creator, pro]
                  message:
                    type: string
  /api/jobs/{jobId}:
    get:
      summary: Get job status and results
      parameters:
        - in: path
          name: jobId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Job found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobRecord'
        '404':
          description: Job not found
  /api/usage:
    get:
      summary: Get usage summary for the authenticated customer
      parameters:
        - in: header
          name: Authorization
          required: false
          schema:
            type: string
            example: Bearer sk_test_xxx
      responses:
        '200':
          description: Usage summary
          content:
            application/json:
              schema:
                type: object
                properties:
                  customer_id:
                    type: string
                  plan:
                    type: string
                    enum: [starter, creator, pro]
                  used:
                    type: integer
                  allowance:
                    type: integer
                  remaining:
                    type: integer
components:
  schemas:
    Chapter:
      type: object
      properties:
        start:
          type: string
          example: '00:00:00'
        end:
          type: string
          example: '00:07:12'
        duration_sec:
          type: integer
          example: 432
        title:
          type: string
          example: 'Intro and objectives'
        summary:
          type: string
          nullable: true
    JobRecord:
      type: object
      properties:
        job_id:
          type: string
        video_id:
          type: string
        status:
          type: string
          enum: [queued, processing, completed, failed]
        estimated_minutes:
          type: integer
        billing_preview_minutes:
          type: integer
        minutes_billed:
          type: integer
          nullable: true
        customer_id:
          type: string
          nullable: true
        chapters:
          type: array
          items:
            $ref: '#/components/schemas/Chapter'
          nullable: true
        error:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time
          nullable: true

