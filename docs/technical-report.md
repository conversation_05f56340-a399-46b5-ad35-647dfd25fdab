## Technical Report (v2)
### Bulk YouTube Chapter Generator – Scalable, Length-Based, Groq-Powered

### 1) Executive Summary
- Paste one YouTube URL → get accurate, timestamped chapters ready to upload.
- Single API, async pipeline, metered billing per rendered minute with monthly allowances.
- Length-based segmentation for predictable cost/latency; LLMs mainly for titling/refinement.
- Targets: p95 < 30 s (for 60-min video), ≥85% margin at scale, >25k videos/day throughput.

### 2) Market Context & Positioning
- **TL;DR**: YouTube’s built-in auto-chapters are free but flaky; there’s a sizable, paying niche for a polished third-party tool. You’re competing with manual work and inaccurate defaults—not a perfect free alternative.

#### What YouTube actually gives creators today
| Feature | What it does | Limitations that create opportunity |
|---------|--------------|--------------------------------------|
| **Automatic chapters** (opt-in) | Adds generic “Section 1, Section 2 …” based on audio pauses | • Only ~60% of videos qualify<br>• Titles are non-SEO, no keywords<br>• Cannot be batch-edited<br>• Frequent false splits |
| **Manual chapters** | Creator types `00:00 Intro …` in description | • Tedious on 30–60 min content<br>• No bulk/library tool<br>• Zero AI title optimization |

#### Evidence that creators still pay
- LenosTube (freemium) and TimeSkip (paid extension) have thousands of paid users despite auto-chapters.
- Reddit r/PartneredYouTube: recurring “auto-chapters are garbage; what tool do you use?” threads.
- vidIQ 2024: ~41% of creators manually overwrite auto-chapters every upload.

#### Positioning cheat-sheet
| Pain you solve | YouTube auto-chapter status |
|----------------|-----------------------------|
| **SEO titles** | Generic placeholders only |
| **Bulk library** | One-at-a-time, no API |
| **Accuracy** | Misses visual cuts/music changes |
| **Workflow** | Still forces Studio clicks |

#### Bottom line
- Free is fine for hobbyists; unreliable for growth-focused channels. Competes with human labor, making $9–$29/mo easy to justify (Pro at $99 for power users).

### 3) Scope
- Single endpoint: POST `/generate` → returns `job_id`; results delivered via WebSocket and persisted in DynamoDB.
- Billing unit: Per rendered minute. Plans grant monthly minutes with overage.

### 4) Functional Requirements
- **FR-1 Input**: YouTube URL only; optional hints (language, target chapter density).
- **FR-2 Plans**:
  - Starter: 120 min
  - Creator: 400 min
  - Pro: 1,600 min
  - Overage: $0.05/min (Stripe metered)
  - Max single video length: 120 min (hard gate)
- **FR-3 Output**: Ordered list of chapters with `start`, `end`, `duration`, `title`, `summary`.
- **FR-4 Behavior**: Idempotent per canonical `video_id`; reuse cached results if transcript unchanged.
- **FR-5 Latency**: p95 < 30 s for a 60-min video to first chapter set (async; titles may refine later).
- **FR-6 International**: Auto language detection; choose Whisper model accordingly.

### 5) High-Level Flow
Client → Next.js Edge API → S3 pre-signed upload → EventBridge → Step Functions → Groq Whisper → GPT titles → DynamoDB → WebSocket push

```mermaid
sequenceDiagram
  participant Client
  participant Edge as Next.js Edge API
  participant S3 as Amazon S3
  participant EB as EventBridge
  participant SFN as Step Functions (Express)
  participant Groq as Groq Whisper
  participant LLM as GPT (titles)/Groq (chapters)
  participant DDB as DynamoDB
  participant WS as WebSocket Gateway
  participant Stripe as Stripe Metered Billing

  Client->>Edge: POST /generate { url, options }
  Edge->>Edge: Auth + plan/quota check + idempotency
  Edge->>S3: Create pre-signed URL (audio bucket)
  Edge->>EB: PutEvent VideoSubmitted { job_id, video_id, s3_key }
  EB->>SFN: StartExecution(job_id)
  SFN->>Groq: Transcribe S3 audio (Whisper)
  Groq-->>SFN: Transcript + timing
  SFN->>LLM: Chapters + title generation
  LLM-->>SFN: Chapters + candidate titles
  SFN->>DDB: Persist (status=completed)
  SFN->>Stripe: Create UsageRecord (minutes)
  SFN->>WS: job.completed { job_id, video_id }
  WS-->>Client: Push results
```

### 6) API Design
- **Auth**: Bearer JWT or service key; token maps to Stripe `customer_id`.
- **Rate limits**: WAF 120 req/min/IP; app-level 60 req/min/user.
- **Idempotency**: `Idempotency-Key` header + canonical `video_id`.

Request (POST `/generate`)
```json
{
  "url": "https://www.youtube.com/watch?v=VIDEO_ID",
  "options": {
    "chapter_density": "auto|sparse|dense",
    "language_hint": "auto|en|es|fr|de|...",
    "title_style": "concise|descriptive",
    "include_summaries": true
  }
}
```

Response (202 Accepted)
```json
{
  "job_id": "job_01HXYZ...",
  "video_id": "VIDEO_ID",
  "estimated_minutes": 60,
  "billing_preview_minutes": 60,
  "status": "queued",
  "ws_channel": "user_{customer_id}"
}
```

Polling fallback (GET `/jobs/{job_id}`) and WebSocket event `job.completed`.

Result payload
```json
{
  "job_id": "job_01HXYZ...",
  "video_id": "VIDEO_ID",
  "chapters": [
    {
      "start": "00:00:00",
      "end": "00:07:12",
      "duration_sec": 432,
      "title": "Intro and objectives",
      "summary": "High-level overview of the topic"
    }
  ],
  "minutes_billed": 60,
  "model_trace": {
    "transcribe": { "engine": "Groq/Whisper-large-v3", "rtf": 0.2 },
    "chapters": { "engine": "Groq LLM", "tokens": 4200 },
    "titles": { "engine": "GPT-4o-mini", "tokens": 1000 }
  }
}
```

Errors: 400 invalid URL, 401 unauthorized, 402 quota exceeded, 413 video too long (>120 min), 429 rate limited, 5xx transient.

### 7) System Architecture
- Next.js Edge API: Auth, quota checks, idempotency, URL normalization, enqueue.
- S3: Audio storage (encrypted, 24h TTL); pre-signed URLs expire in 15 min.
- EventBridge: Decoupled orchestration trigger; retries with DLQ.
- Step Functions (Express): Transcribe → Segment → Title → Persist → Bill → Notify.
- Groq Whisper: Fast ASR; model per language.
- LLMs: Groq for segmentation refinement; GPT for concise titles.
- DynamoDB: On-demand tables; TTL 24h for raw/transient.
- WebSocket: API Gateway or server WebSocket for near-real-time push.
- Stripe: Metered billing via Usage Records linked to `customer_id`.

### 8) Data Model (DynamoDB)
- `videos`
  - PK `video_id` (S), SK `v#<major>` (S)
  - `duration_sec` (N), `language` (S), `status` (S), `created_at` (S), `minutes_billed` (N)
  - GSI1: `customer_id` + `created_at`
- `chapters`
  - PK `video_id` (S), SK `c#<sequence>` (S)
  - `start`, `end`, `duration_sec`, `title`, `summary`
- `transcripts` (TTL 24h)
  - PK `video_id` (S); `segments` (L), `language`, `model_meta`, `expires_at` (N)
- `jobs` (optional)
  - PK `job_id` (S); `video_id`, `status`, `error`, `started_at`, `completed_at`

Idempotency key = hash of `(video_id, audio_etag|transcript_hash, options_fingerprint)`.

### 9) Length-Based Chaptering Algorithm
- Target chapter count: \( n = \operatorname{clamp}\big(\lceil \frac{duration_{min}}{6} \rceil, 6, 25\big) \)
- Step 1: Pre-segment into \( n \) slices with ±30% dynamic breathing.
- Step 2: Boundary refinement using:
  - Silence/energy dips from ASR timestamps
  - Lexical cues (“next”, “moving on”, headings)
  - Paragraph boundaries from Whisper segments
- Step 3: Titles and optional summaries via small LLM pass per segment.
- Deterministic fallback if LLM unavailable.

### 10) Billing & Metering
- Monthly allowances: Starter 120, Creator 400, Pro 1,600 minutes.
- Overage: $0.05/min via Stripe metered billing.
- Minutes billed = `ceil(duration_sec/60)`.
- Step Functions posts usage at job completion time.
- Stripe is source of truth; local mirror for UX with daily reconciliation.

### 11) Cost Model (Groq stack)
- Unit cost = $0.004 (Groq) + $0.001 (AWS egress/S3/CF) = $0.005/min.

| Plan | Allowance (min) | AWS Cost | Revenue | Gross Margin |
|------|------------------|----------|---------|--------------|
| Starter | 120 | $0.60 | $9 | 93% |
| Creator | 400 | $2.00 | $29 | 93% |
| Pro | 1,600 | $8.00 | $99 | 92% |

Assumptions: Whisper RTF ~0.2–0.5 on Groq; LLM tokens negligible vs ASR; S3 egress amortized via CF.

### 12) Scalability Limits
- Groq concurrency soft limit 600 req/min → ~25k videos/day at avg 10 min.
- Step Functions Express 10k concurrent → >60k videos/day.
- DynamoDB on-demand scales to millions of minutes/day; adaptive capacity enabled.

### 13) Security & Compliance
- S3 pre-signed URLs expire in 15 min; buckets encrypted (SSE-KMS).
- Least-privilege IAM; scoped KMS grants.
- DynamoDB TTL 24h for transcripts; chapters retained per product policy.
- No PII beyond `customer_id`, `video_id`, chapter list.
- WAF: 120 requests/min/IP; geo/bot filters.
- Secrets in AWS Secrets Manager; CloudTrail and Stripe logs retained 1 year.

### 14) Observability & SLOs
- SLOs: p95 generation latency < 30 s (60-min input); success rate > 99.5%.
- Metrics: RTF, queue delay, SFN duration, token usage, minutes billed, error classes.
- Tracing: AWS X-Ray across Edge → SFN → Groq/LLMs.
- Alerts: p95 > 30 s; DLQ > 10; Stripe usage post failures > 5/min.

### 15) DevOps & Delivery
- Monorepo: `apps/web` (Next.js), `apps/api` (Edge routes), `packages/infra` (`serverless-compose.yml`).
- CI: GitHub Actions (build, lint, test, infra plan/apply on PR).
- Deploy: `npm run deploy` → Edge + infra; Step Functions canary via CodeDeploy (linear 10%/5m).
- Rollback: Versioned stacks; blue/green for SFN tasks.

### 16) Testing Strategy
- Unit: URL parsing, idempotency, quota calc, minute rounding.
- Contract: OpenAPI for `/generate`, WebSocket events.
- Integration: Mock Groq/LLM; real S3/DDB in ephemeral env.
- Load: 1k concurrent submissions; verify quotas and p95.
- Chaos: Groq timeouts (429/5xx), DLQ drains, Stripe retry paths.

### 17) Risks & Mitigations
- Groq throttling: Backoff + token bucket; overflow queue; warm pool preallocation.
- Transcript quality: Auto-detect language; retry larger model; confidence gating.
- LLM variance: Deterministic decoding for titles; rule-based fallback.
- Stripe latency: Idempotent retries; nightly reconciliation of local ledger vs Stripe.
- Very long videos: Hard gate at 120 min; suggest splitting; future split/merge batch mode.
- YouTube fetch changes: Compliant server-side fetchers; user-upload fallback.

### 18) Roadmap
- Bulk ingestion (CSV, playlist)
- YouTube API: auto-upload chapters
- Team seats; shared allowances
- Multi-language titling; glossary protection
- On-the-fly chapter density slider

### 19) KPIs
- p95 generation latency < 30 s for 60-min video.
- Margin ≥ 85% at 10k videos/day.
- Churn ≤ 5% monthly.

### 20) Acceptance Criteria
- Endpoint deployable in < 5 minutes; plans 120/400/1600 minutes live.
- p95 < 30 s for 60-min video; success rate > 99.5% for valid inputs.
- Billing accurate to the minute; Stripe usage records reconciled daily.
- Gross margin ≥ 92% on Pro and ≥93% on Starter/Creator at listed pricing.

### 21) Deliverable
- One repo deploys in < 5 min (`npm run deploy`) and immediately offers 120 / 400 / 1,600 monthly minutes at ~92–93% margin.

- Adjusted allowances to 120/400/1600, updated cost/margin table, and embedded your positioning section.
- Full markdown is ready to drop into the repo/docs or a Notion page.