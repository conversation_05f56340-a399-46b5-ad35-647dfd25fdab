## Release TODO Checklist

Scope: Bulk YouTube Chapter Generator — MVP to Production. Plans: 120 / 400 / 1,600 minutes; overage $0.05/min; max single video 120 min.

### Milestone 0 — Project Scaffold
- [x] Monorepo init with workspaces (`apps/*`, `packages/*`)
- [x] Next.js app scaffold in `apps/web`
- [x] Stub Edge API route `POST /api/generate` returning 202 Accepted
- [x] Add `docs/technical-report.md` to repo (to wire to site later)

Acceptance: `npm run build` passes; `/api/generate` responds 202 in dev.

### Milestone 1 — API Contract & Idempotency
- [x] Define OpenAPI spec for `POST /generate` and `GET /jobs/{job_id}`
- [x] Implement Edge `POST /api/generate` with:
  - [x] Auth (Bearer) parsing; map token → `customer_id`
  - [x] URL canonicalization → `video_id`
  - [x] Idempotency via `Idempotency-Key` + `(video_id, options_hash)`
  - [x] Length gate: reject if `duration > 120 min` (YouTube Data API fallback to mock)
  - [x] Enqueue event (local bus) with `job_id`
- [x] Implement `GET /api/jobs/[job_id]`

Acceptance: Contract tests pass; idempotent resubmission returns same `job_id`.

### Milestone 2 — Storage & Data Model (DynamoDB)
- [x] Create local table: `jobs` (DynamoDB Local)
- [ ] Create tables: `videos`, `chapters`, `transcripts` (TTL 24h)
- [ ] Add GSI for `customer_id` + `created_at`
- [x] Implement writes: job lifecycle and chapters (memory + DynamoDB)
- [x] Infra: add `JobsTable` with GSI `gsi_customer_created` in `serverless-compose.yml`
- [x] Dev: persist in-memory job store across hot reloads (attach to `globalThis`)
- [x] Dev: ensure `GET /api/jobs/[job_id]` returns saved job using the same provider

Acceptance: End-to-end job writes visible; TTL policy verified on `transcripts`.

### Milestone 3 — Ingestion & Transcription (Groq Whisper)
- [ ] S3 bucket (audio, TTL 24h, SSE-KMS); pre-signed uploads (15 min expiry)
- [ ] Server-side download YT → S3 (with backoff)
- [x] Server-side fetch YouTube audio stream → Groq Whisper (no S3 yet)
- [x] Capture segments + language; configurable model (default large‑v3; turbo optional)
- [ ] Step Functions task wrapper (cloud)

Acceptance: Known 10-min video produces transcript within latency budget.

### Milestone 4 — Chapter Segmentation & Titles
- [x] Length-based pre-segmentation: `n = clamp(ceil(duration_min/6), 6, 25)` (even segments)
- [x] Transcript-based segmentation using Whisper segments (target bucket count)
- [ ] Boundary refinement using silence/energy + lexical cues
- [x] Titles and optional summaries (heuristic placeholder)
- [x] Deterministic fallback when LLM unavailable (length-based)

Acceptance: Outputs valid YouTube chapters for sample videos; consistent count.

### Milestone 5 — Orchestration
- [x] Local event bus → simulated SFN (auto-drain)
- [x] DLQ and retries configured (in-memory)
- [ ] EventBridge rule → Step Functions (cloud)

Acceptance: Job completes on retries; DLQ alarms fire on poison messages.

### Milestone 6 — Billing & Plans (Stripe)
- [x] Allowances 120/400/1600 and 402 gate
- [x] Minute calc: `ceil(duration_sec/60)`
- [x] Post usage on completion (stub + real Stripe usage when env set)
- [x] Monthly allowance enforcement in API
- [ ] Stripe products/plans and real Usage Records

Acceptance: Simulated usage rolls into allowance; overage billed; reconciliation passes.

### Milestone 7 — WebSocket Notifications
- [ ] WebSocket gateway/channel per customer
- [x] Push `job.completed` with results via SSE (server-sent events)
- [x] Fallback polling path works

Acceptance: Frontend receives real-time completion updates reliably.

### Milestone 8 — Security & Compliance
- [ ] Least-privilege IAM for each component
- [ ] Secrets in AWS Secrets Manager
- [ ] WAF rate limit 120 req/min/IP; bot/geo filters
- [ ] No PII beyond `customer_id`, `video_id`, chapters; audit CloudTrail
- [x] API key authentication (Bearer) mapped to `customer_id` & `plan` via `API_KEYS_JSON`

Acceptance: Security review checklist passes; WAF tested with load.

### Milestone 9 — Observability & SLOs
- [ ] Metrics: RTF, queue delay, SFN duration, token usage, minutes billed
- [ ] Tracing with X-Ray across Edge → SFN → LLM calls
- [ ] Alerts: p95 > 30s, DLQ > 10, Stripe post failures > 5/min

Acceptance: Dashboards live; synthetic tests trigger alarms appropriately.

### Milestone 10 — Frontend MVP
- [x] Submit URL form; show job status and chapters
- [x] Copy/download chapters and copy JSON
- [x] Account page: plan usage
- [x] Options: density, title style, include summaries
- [x] Jobs page with recent jobs
- [x] Error handling for 402/400/413 with banners
- [ ] Payment/upgrade UI

Acceptance: Demo flow from URL → chapters visible and copyable.

### Milestone 11 — DevOps & Deploy
- [ ] `serverless-compose.yml` infra definitions
- [ ] GitHub Actions (build, lint, test, infra plan/apply, preview env)
- [ ] Canary deploy for Step Functions via CodeDeploy
- [ ] Rollback strategy (blue/green for SFN)
- [ ] Ensure prod storage uses DynamoDB (`NEXT_PUBLIC_STORAGE=dynamodb`, `AWS_REGION`, `JOBS_TABLE`)

Acceptance: `npm run deploy` provisions infra and deploys in < 5 min.

### Milestone 12 — QA & Launch
- [ ] Load test (1k concurrent submissions) within quotas
- [ ] Chaos tests: Groq 429/5xx, network failures, DLQ drains
- [ ] Docs: pricing, limits, SLA, privacy
- [ ] Changelog v1.0.0 and status page

Acceptance: KPIs met — p95 < 30s (60-min input), success > 99.5%, margins ≥ 92% Pro.

---

### Backlog / Roadmap
- [ ] Bulk ingestion (CSV, playlist)
- [ ] YouTube API auto-upload of chapters
- [ ] Team seats & shared allowances
- [ ] Multi-language titling & glossary protection
- [ ] Chapter density slider (user-adjustable)

### Engineering Notes
- Validate YouTube URL variants (`youtu.be`, `&t=`, playlists)
- Always `ceil` minutes to avoid under-billing
- Idempotency key: hash(`video_id`, `audio_etag|transcript_hash`, `options_fingerprint`)
- Ensure titles are concise, keyworded; timestamps `HH:MM:SS`
- Fix lints as errors in CI (no unused vars, etc.)
- YouTube duration fetch with short in-memory cache; real Data API when `YOUTUBE_API_KEY` set
- Transcription path downloads audio stream from YouTube on server; model configurable via `GROQ_WHISPER_MODEL`
- Local dev: in-memory store persists across hot reloads via `globalThis`
- Production: storage backend is DynamoDB (set `NEXT_PUBLIC_STORAGE=dynamodb`, `AWS_REGION`, `JOBS_TABLE`)
